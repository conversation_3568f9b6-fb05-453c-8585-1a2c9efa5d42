
import os
import time
import hashlib
import base64
import requests
import json
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend
import threading
import sys
import tkinter as tk
from tkinter import simpledialog, messagebox, ttk

def get_application_path():
    """获取应用程序所在目录，兼容打包后的可执行文件"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        return os.path.dirname(sys.executable)
    else:
        # 如果是源代码运行
        return os.path.dirname(os.path.abspath(__file__))

def clear_line():
    """清除当前行并回到行首"""
    print("\033[2K\033[1G", end="")

def create_popup_menu(widget):
    """为控件创建右键菜单（复制、粘贴功能）"""
    popup = tk.Menu(widget, tearoff=0)
    popup.add_command(label="复制", command=lambda: widget.event_generate('<<Copy>>'))
    popup.add_command(label="粘贴", command=lambda: widget.event_generate('<<Paste>>'))
    popup.add_separator()
    popup.add_command(label="全选", command=lambda: widget.select_range(0, 'end'))
    
    def show_menu(event):
        popup.post(event.x_root, event.y_root)
    
    widget.bind("<Button-3>", show_menu)
    return popup

def get_license_key():
    """显示GUI输入框获取卡密"""
    root = tk.Tk()
    root.title("卡密验证")
    root.geometry("600x200")
    root.resizable(True, False)
    root.configure(bg="#f0f0f0")
    root.eval('tk::PlaceWindow . center')
    
    label = tk.Label(
        root, 
        text="请输入您的卡密:", 
        font=("Microsoft YaHei", 12),
        bg="#f0f0f0",
        pady=20
    )
    label.pack()
    
    entry_var = tk.StringVar()
    entry = tk.Entry(
        root, 
        textvariable=entry_var, 
        font=("Microsoft YaHei", 12),
        width=40
    )
    entry.pack(pady=10)
    entry.focus_set()
    create_popup_menu(entry)
    
    button_frame = tk.Frame(root, bg="#f0f0f0")
    button_frame.pack(pady=20)
    
    result = [None]
    
    def on_confirm():
        result[0] = entry_var.get()
        root.destroy()
    
    def on_cancel():
        root.destroy()
    
    confirm_btn = tk.Button(
        button_frame, 
        text="确认", 
        command=on_confirm,
        font=("Microsoft YaHei", 10),
        width=10,
        bg="#4CAF50",
        fg="white"
    )
    confirm_btn.pack(side=tk.LEFT, padx=10)
    
    cancel_btn = tk.Button(
        button_frame, 
        text="取消", 
        command=on_cancel,
        font=("Microsoft YaHei", 10),
        width=10,
        bg="#f44336",
        fg="white"
    )
    cancel_btn.pack(side=tk.LEFT, padx=10)
    
    root.bind('<Return>', lambda event: on_confirm())
    root.mainloop()
    
    return result[0]

def save_license_key(license_key):
    """保存卡密到文件"""
    try:
        script_dir = get_application_path()
        license_file = os.path.join(script_dir, ".license")
        
        with open(license_file, "w") as f:
            f.write(license_key)
            
        return True
    except Exception as e:
        print(f"保存卡密文件失败: {e}")
        return False

class LicenseValidator:
    """卡密验证器，用于验证卡密有效性并检查是否过期"""
    
    def __init__(self, master_key: str):
        self.master_key = master_key.encode('utf-8')
        self.aes_key = hashlib.sha256(self.master_key).digest()[:16]
    
    def _unformat_license_key(self, license_key: str) -> str:
        cleaned_key = ''.join(filter(lambda c: c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=', license_key))
        
        if len(cleaned_key) < 15:
            raise ValueError("卡密长度不足")
        
        prefix = cleaned_key[:3]
        checksum = cleaned_key[-4:]
        encrypted = cleaned_key[3:-4]
        
        if not (prefix.startswith('L') and len(prefix) == 3 and prefix[1:].isdigit()):
            raise ValueError("卡密前缀格式不正确")
        
        checksum_data = f"{prefix}{encrypted}"
        expected_checksum = hashlib.md5(checksum_data.encode('utf-8')).hexdigest()[:4]
        
        if checksum != expected_checksum:
            raise ValueError(f"卡密校验失败，预期: {expected_checksum}, 实际: {checksum}")
        
        missing_padding = len(encrypted) % 4
        if missing_padding:
            encrypted += '=' * (4 - missing_padding)
        
        return encrypted
    
    def _unpad_data(self, padded_data: bytes) -> bytes:
        unpadder = padding.PKCS7(128).unpadder()
        return unpadder.update(padded_data) + unpadder.finalize()
    
    def _decrypt(self, ciphertext: str) -> str:
        try:
            encrypted_data = base64.b64decode(ciphertext)
            
            if len(encrypted_data) < 16:
                raise ValueError(f"加密数据长度过短: {len(encrypted_data)}")
            if len(encrypted_data) % 16 != 0:
                raise ValueError(f"加密数据长度不是16的倍数: {len(encrypted_data)}")
            
            iv = encrypted_data[:16]
            ciphertext = encrypted_data[16:]
            
            cipher = Cipher(algorithms.AES(self.aes_key), modes.CBC(iv), backend=default_backend())
            decryptor = cipher.decryptor()
            
            decrypted_padded = decryptor.update(ciphertext) + decryptor.finalize()
            decrypted = self._unpad_data(decrypted_padded)
            
            return decrypted.decode('utf-8')
        
        except Exception as e:
            raise ValueError(f"解密失败: {e}")
    
    def get_network_time(self) -> int:
        try:
            response = requests.get("https://f.m.suning.com/api/ct.do", timeout=3)
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == "1" and "currentTime" in data:
                    return int(data["currentTime"]) // 1000
        except Exception:
            pass
        
        local_time = int(time.time())
        if time.localtime(local_time).tm_gmtoff // 3600 != 8:
            local_time += 8 * 3600
        return local_time
    
    def format_remaining_time(self, remaining_seconds: int) -> str:
        if remaining_seconds < 0:
            return "已过期"

        # 如果剩余时间小于1分钟，显示秒数
        if remaining_seconds < 60:
            return f"{remaining_seconds}秒"

        # 如果剩余时间小于1小时，显示分钟和秒数
        if remaining_seconds < 3600:
            minutes, seconds = divmod(remaining_seconds, 60)
            return f"{minutes}分钟{seconds}秒"

        hours, remainder = divmod(remaining_seconds, 3600)
        minutes = remainder // 60

        if hours >= 24:
            days = hours // 24
            hours = hours % 24
            return f"{days}天{hours}小时{minutes}分钟"
        else:
            return f"{hours}小时{minutes}分钟"
    
    def verify_license_key(self, license_key: str, expected_product_id: str = None) -> dict:
        result = {
            "valid": False,
            "expired": False,
            "remaining_seconds": 0,
            "remaining_time_formatted": "",
            "message": "",
            "data": None
        }
        
        try:
            encrypted_data = self._unformat_license_key(license_key)
            decrypted_data = self._decrypt(encrypted_data)
            license_data = json.loads(decrypted_data)
            
            signature_data = license_data.copy()
            original_signature = signature_data.pop("signature", "")
            signature_json = json.dumps(signature_data, sort_keys=True)
            calculated_signature = hashlib.sha256((signature_json + self.master_key.decode('utf-8')).encode('utf-8')).hexdigest()[:8]
            
            if original_signature != calculated_signature:
                result["message"] = "卡密签名验证失败"
                return result
            
            if expected_product_id is not None and license_data["product_id"] != expected_product_id:
                result["message"] = f"卡密产品ID不匹配，预期: {expected_product_id}，实际: {license_data['product_id']}"
                return result
            
            current_time = self.get_network_time()
            
            if current_time > license_data["expire_time"]:
                result["expired"] = True
                result["message"] = "卡密已过期"
            else:
                result["valid"] = True
                result["remaining_seconds"] = license_data["expire_time"] - current_time
                result["remaining_time_formatted"] = self.format_remaining_time(result["remaining_seconds"])
                result["message"] = "卡密有效"
            
            result["data"] = license_data
            
        except Exception as e:
            result["message"] = f"验证错误: {str(e)}"
        
        return result

class LicenseManager:
    """许可证管理器，处理许可证验证和管理"""
    
    def __init__(self, product_id: str, master_key: str):
        self.product_id = product_id
        self.validator = LicenseValidator(master_key)
        self.license_data = None
        self.license_expired = False
        self.script_dir = get_application_path()
        self.license_file = os.path.join(self.script_dir, ".license")
    
    def verify_license(self) -> bool:
        try:
            # 如果许可证文件不存在，提示用户输入卡密
            if not os.path.exists(self.license_file):
                print("未找到许可证文件，请输入卡密进行验证...")
                
                while True:
                    license_key = get_license_key()
                    
                    if license_key is None:
                        print("用户取消了验证，程序退出")
                        return False
                    
                    if not license_key.strip():
                        messagebox.showerror("验证失败", "卡密不能为空!")
                        continue
                    
                    result = self.validator.verify_license_key(license_key, self.product_id)
                    
                    if result["valid"]:
                        self.license_data = result["data"]
                        
                        if save_license_key(license_key):
                            messagebox.showinfo("验证成功", f"卡密验证成功!剩余时间: {result['remaining_time_formatted']}")
                            return True
                        else:
                            messagebox.showerror("保存失败", "无法保存卡密文件，请检查写入权限")
                    else:
                        messagebox.showerror("验证失败", f"卡密无效: {result['message']}")
            else:
                with open(self.license_file, "r") as f:
                    license_key = f.read().strip()
                
                result = self.validator.verify_license_key(license_key, self.product_id)
                
                if not result["valid"]:
                    print(f"错误: 许可证无效 - {result['message']}")
                    try:
                        os.remove(self.license_file)
                    except Exception as e:
                        print(f"删除无效许可证文件失败: {e}")
                    return False
                    
                if result["expired"]:
                    print("错误: 许可证已过期")
                    try:
                        os.remove(self.license_file)
                    except Exception as e:
                        print(f"删除过期许可证文件失败: {e}")
                    return False
                    
                self.license_data = result["data"]
                return True
                
        except Exception as e:
            print(f"验证许可证时出错: {e}")
            return False
    
    def check_license_periodically(self):
        check_interval = 60  # 每分钟检查一次
        warning_shown = False

        while True:
            time.sleep(check_interval)

            try:
                # 检查许可证状态
                if os.path.exists(self.license_file):
                    with open(self.license_file, "r") as f:
                        license_key = f.read().strip()

                    result = self.validator.verify_license_key(license_key, self.product_id)

                    if result["valid"]:
                        remaining_seconds = result["remaining_seconds"]

                        # 如果剩余时间少于5分钟，显示警告
                        if remaining_seconds <= 300 and not warning_shown:
                            warning_shown = True
                            remaining_time = self.validator.format_remaining_time(remaining_seconds)
                            print(f"\n⚠️  警告: 许可证即将过期！剩余时间: {remaining_time}")

                        # 如果剩余时间少于1分钟，每10秒检查一次
                        if remaining_seconds <= 60:
                            check_interval = 10
                            remaining_time = self.validator.format_remaining_time(remaining_seconds)
                            print(f"\n⏰ 许可证即将过期！剩余时间: {remaining_time}")

                        continue

                    # 许可证无效或过期
                    if result["expired"]:
                        self._handle_license_expiry("许可证已过期")
                    else:
                        self._handle_license_expiry(f"许可证无效: {result['message']}")
                else:
                    self._handle_license_expiry("许可证文件丢失")

            except Exception as e:
                self._handle_license_expiry(f"检查许可证时出错: {e}")

    def _handle_license_expiry(self, reason: str):
        """处理许可证过期或无效的情况"""
        self.license_expired = True

        print(f"\n❌ {reason}")

        # 删除无效的许可证文件
        try:
            if os.path.exists(self.license_file):
                os.remove(self.license_file)
                print("已删除无效的许可证文件")
        except Exception as e:
            print(f"删除许可证文件失败: {e}")

        # 倒计时退出
        countdown_seconds = 30
        print(f"\n程序将在 {countdown_seconds} 秒后自动退出...")

        for i in range(countdown_seconds, 0, -1):
            clear_line()
            print(f"⏱️  退出倒计时: {i} 秒 (按 Ctrl+C 立即退出)", end="", flush=True)
            time.sleep(1)

        clear_line()
        print("\n程序已关闭")
        sys.exit(1)

# 初始化许可证管理器
_license_manager = LicenseManager("1", "YourSecretMasterKey1234567890")

# 初始验证
if __name__ == "__main__":
    if not _license_manager.verify_license():
        import sys
        sys.exit(1)
    
    if _license_manager.license_data is not None:
        print(f"许可证有效，剩余时间: {_license_manager.validator.format_remaining_time(_license_manager.license_data['expire_time'] - _license_manager.validator.get_network_time())}")
    else:
        print("许可证验证成功，但无法获取许可证详细信息")
    
    # 启动定时检查线程
    _check_thread = threading.Thread(target=_license_manager.check_license_periodically, daemon=True)
    _check_thread.start()
    
    # 验证成功后不退出，继续执行程序的其他部分
    print("程序正在启动...")
    # 这里是原始程序的入口点
    # 如果你有main函数，可以在这里调用
    # main()


# ===== 原始代码 =====
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows安全中心排除项管理器
使用PyQt6框架开发的可视化工具，用于管理Windows Defender的排除项
"""

import sys
import os
import subprocess
import ctypes
import json
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QPushButton, QTextEdit, QListWidget, QListWidgetItem, QLabel,
    QMessageBox, QMenu, QSplitter, QFrame, QGroupBox, QLineEdit,
    QFileDialog, QToolButton, QGridLayout
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QMimeData, QTimer
from PyQt6.QtGui import QIcon, QDragEnterEvent, QDropEvent, QAction, QFont


class PowerShellWorker(QThread):
    """PowerShell命令执行线程"""
    finished = pyqtSignal(bool, str)
    log_message = pyqtSignal(str)
    
    def __init__(self, command: str, operation: str):
        super().__init__()
        self.command = command
        self.operation = operation
    
    def run(self):
        try:
            self.log_message.emit(f"[{datetime.now().strftime('%H:%M:%S')}] 开始执行: {self.operation}")

            # 使用管理员权限执行PowerShell命令
            result = subprocess.run(
                ["powershell", "-Command", self.command],
                capture_output=True,
                text=True,
                shell=True,
                timeout=30,
                creationflags=subprocess.CREATE_NO_WINDOW  # 隐藏PowerShell窗口
            )

            if result.returncode == 0:
                self.log_message.emit(f"[{datetime.now().strftime('%H:%M:%S')}] ✓ {self.operation} 成功")
                self.finished.emit(True, result.stdout.strip())
            else:
                error_msg = result.stderr.strip() or "未知错误"
                self.log_message.emit(f"[{datetime.now().strftime('%H:%M:%S')}] ✗ {self.operation} 失败: {error_msg}")
                self.finished.emit(False, error_msg)

        except subprocess.TimeoutExpired:
            self.log_message.emit(f"[{datetime.now().strftime('%H:%M:%S')}] ✗ {self.operation} 超时")
            self.finished.emit(False, "操作超时")
        except Exception as e:
            self.log_message.emit(f"[{datetime.now().strftime('%H:%M:%S')}] ✗ {self.operation} 异常: {str(e)}")
            self.finished.emit(False, str(e))


class DefenderExclusionManager(QMainWindow):
    """Windows Defender排除项管理器主窗口"""
    
    def __init__(self):
        super().__init__()
        try:
            self.current_exclusions = []
            self.current_dep_apps = []
            self.worker = None  # 初始化worker变量
            self.init_ui()
            self.check_admin_privileges()
            # 延迟刷新，避免初始化时的线程问题
            QTimer.singleShot(2000, self.safe_refresh_exclusions)
            QTimer.singleShot(3000, self.safe_refresh_dep_list)
        except Exception as e:
            self.log_message(f"初始化错误: {str(e)}")

    def safe_refresh_exclusions(self):
        """安全的刷新排除项"""
        try:
            self.refresh_exclusions()
        except Exception as e:
            self.log_message(f"刷新排除项失败: {str(e)}")

    def safe_refresh_dep_list(self):
        """安全的刷新DEP程序列表"""
        try:
            self.refresh_dep_list()
        except Exception as e:
            self.log_message(f"刷新DEP程序列表失败: {str(e)}")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("Windows安全中心排除项管理器 - 作者：吖贤|微信：jk904197932|公众号：抖小贤|主营业务：批量剪辑，批量发布,监控发布等黑科技")
        self.setGeometry(100, 100, 800, 600)
        
        # 设置图标
        icon_path = os.path.join(os.path.dirname(__file__), "图标.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        main_layout.addWidget(splitter)
        
        # 上半部分：拖拽区域和按钮
        top_frame = QFrame()
        top_layout = QVBoxLayout(top_frame)
        
        # 文件选择提示区域
        self.info_label = QLabel("使用下方的路径输入框和浏览按钮来添加文件或文件夹")
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.info_label.setStyleSheet("""
            QLabel {
                border: 2px solid #4CAF50;
                border-radius: 10px;
                padding: 20px;
                font-size: 14px;
                color: #2E7D32;
                background-color: #E8F5E8;
                font-weight: bold;
            }
        """)
        self.info_label.setMinimumHeight(60)
        top_layout.addWidget(self.info_label)

        # 手动添加区域
        manual_frame = QFrame()
        manual_layout = QHBoxLayout(manual_frame)
        manual_layout.setContentsMargins(0, 5, 0, 5)

        # 路径输入框
        path_label = QLabel("路径:")
        manual_layout.addWidget(path_label)

        self.path_input = QLineEdit()
        self.path_input.setPlaceholderText("输入或粘贴文件/文件夹路径...")
        self.path_input.returnPressed.connect(self.add_path_from_input)
        # 设置右键菜单
        self.path_input.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.path_input.customContextMenuRequested.connect(self.show_input_context_menu)
        manual_layout.addWidget(self.path_input)

        # 浏览按钮
        self.browse_btn = QPushButton("📁 浏览")
        self.browse_btn.clicked.connect(self.browse_path)
        manual_layout.addWidget(self.browse_btn)

        # 添加到排除项按钮
        self.add_exclusion_btn = QPushButton("➕ 添加到排除项")
        self.add_exclusion_btn.clicked.connect(self.add_path_from_input)
        self.add_exclusion_btn.setToolTip("将路径添加到Windows Defender排除项")
        manual_layout.addWidget(self.add_exclusion_btn)

        # 添加到数据保护按钮
        self.add_protection_btn = QPushButton("🔒 添加到数据保护")
        self.add_protection_btn.clicked.connect(self.add_path_to_dep_protection)
        self.add_protection_btn.setToolTip("将程序文件添加到数据执行保护(DEP)")
        manual_layout.addWidget(self.add_protection_btn)

        top_layout.addWidget(manual_frame)
        
        # 按钮区域
        button_layout = QHBoxLayout()

        self.refresh_btn = QPushButton("🔄 刷新排除项")
        self.refresh_btn.clicked.connect(self.refresh_exclusions)
        button_layout.addWidget(self.refresh_btn)

        self.refresh_dep_btn = QPushButton("🔒 刷新DEP保护")
        self.refresh_dep_btn.clicked.connect(self.refresh_dep_list)
        button_layout.addWidget(self.refresh_dep_btn)

        self.clear_log_btn = QPushButton("🗑️ 清空日志")
        self.clear_log_btn.clicked.connect(self.clear_log)
        button_layout.addWidget(self.clear_log_btn)

        button_layout.addStretch()

        # 快捷添加按钮
        quick_add_label = QLabel("快捷添加:")
        button_layout.addWidget(quick_add_label)

        self.downloads_btn = QPushButton("📥 下载文件夹")
        self.downloads_btn.clicked.connect(self.add_downloads_folder)
        self.downloads_btn.setToolTip("添加用户下载文件夹到排除项")
        button_layout.addWidget(self.downloads_btn)

        self.desktop_btn = QPushButton("🖥️ 桌面")
        self.desktop_btn.clicked.connect(self.add_desktop_folder)
        self.desktop_btn.setToolTip("添加用户桌面文件夹到排除项")
        button_layout.addWidget(self.desktop_btn)

        self.documents_btn = QPushButton("📄 文档")
        self.documents_btn.clicked.connect(self.add_documents_folder)
        self.documents_btn.setToolTip("添加用户文档文件夹到排除项")
        button_layout.addWidget(self.documents_btn)

        self.temp_btn = QPushButton("🗂️ 临时文件")
        self.temp_btn.clicked.connect(self.add_temp_folder)
        self.temp_btn.setToolTip("添加系统临时文件夹到排除项")
        button_layout.addWidget(self.temp_btn)

        top_layout.addLayout(button_layout)
        
        splitter.addWidget(top_frame)
        
        # 下半部分：排除项列表和日志
        bottom_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：排除项和数据保护列表
        left_splitter = QSplitter(Qt.Orientation.Vertical)

        # 排除项列表
        exclusion_group = QGroupBox("当前排除项")
        exclusion_layout = QVBoxLayout(exclusion_group)

        self.exclusion_list = QListWidget()
        self.exclusion_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.exclusion_list.customContextMenuRequested.connect(self.show_exclusion_context_menu)
        exclusion_layout.addWidget(self.exclusion_list)

        left_splitter.addWidget(exclusion_group)

        # 数据执行保护(DEP)列表
        dep_group = QGroupBox("数据执行保护(DEP)程序")
        dep_layout = QVBoxLayout(dep_group)

        self.dep_list = QListWidget()
        self.dep_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.dep_list.customContextMenuRequested.connect(self.show_dep_context_menu)
        dep_layout.addWidget(self.dep_list)

        left_splitter.addWidget(dep_group)

        # 设置左侧分割器比例 (排除项:DEP保护)
        left_splitter.setSizes([300, 200])
        bottom_splitter.addWidget(left_splitter)
        
        # 日志区域
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        bottom_splitter.addWidget(log_group)
        
        # 设置分割器比例
        bottom_splitter.setSizes([500, 400])
        splitter.addWidget(bottom_splitter)
        splitter.setSizes([180, 450])

        # 状态栏
        self.statusBar().showMessage("就绪")

        # 启用拖拽功能
        self.setAcceptDrops(True)

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 清理工作线程
        if self.worker and self.worker.isRunning():
            self.worker.terminate()
            self.worker.wait(3000)  # 等待最多3秒
        event.accept()
    
    def check_admin_privileges(self):
        """检查管理员权限"""
        try:
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if not is_admin:
                self.log_message("⚠️ 警告: 程序未以管理员身份运行，某些操作可能失败")
                self.statusBar().showMessage("警告: 需要管理员权限")
            else:
                self.log_message("✓ 程序正在以管理员身份运行")
                self.statusBar().showMessage("管理员模式")
        except:
            self.log_message("⚠️ 无法检查管理员权限")
    
    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.log_text.append(f"[{timestamp}] {message}")
        self.log_text.ensureCursorVisible()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.log_message("日志已清空")

    def browse_path(self):
        """浏览选择路径"""
        # 先尝试选择文件夹
        folder_path = QFileDialog.getExistingDirectory(
            self,
            "选择文件夹",
            os.path.expanduser("~"),
            QFileDialog.Option.ShowDirsOnly
        )

        if folder_path:
            self.path_input.setText(folder_path)
        else:
            # 如果没有选择文件夹，尝试选择文件
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择文件",
                os.path.expanduser("~"),
                "所有文件 (*.*)"
            )
            if file_path:
                self.path_input.setText(file_path)

    def add_path_from_input(self):
        """从输入框添加路径"""
        path = self.path_input.text().strip()
        if not path:
            self.log_message("⚠️ 请输入路径")
            return

        # 智能处理路径：去除引号
        path = self.clean_path(path)

        # 展开环境变量和用户目录
        path = os.path.expandvars(os.path.expanduser(path))

        if self.add_exclusion(path):
            self.path_input.clear()

    def clean_path(self, path: str) -> str:
        """智能清理路径：去除引号、处理特殊字符"""
        if not path:
            return path

        # 去除首尾空格
        path = path.strip()

        # 去除引号（支持单引号和双引号）
        if (path.startswith('"') and path.endswith('"')) or \
           (path.startswith("'") and path.endswith("'")):
            path = path[1:-1]

        # 再次去除空格（引号内可能有空格）
        path = path.strip()

        return path

    def smart_paste(self):
        """智能粘贴：自动去除引号和处理路径"""
        from PyQt6.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        text = clipboard.text().strip()

        if text:
            # 使用智能路径清理
            cleaned_path = self.clean_path(text)
            self.path_input.setText(cleaned_path)
            self.log_message(f"智能粘贴路径: {cleaned_path}")
        else:
            self.log_message("剪贴板为空")

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            self.info_label.setText("🎯 松开鼠标以添加文件/文件夹到排除项")
            self.info_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            event.ignore()

    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        self.info_label.setText("使用下方的路径输入框和浏览按钮来添加文件或文件夹")
        self.info_label.setStyleSheet("")

    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        try:
            self.info_label.setText("使用下方的路径输入框和浏览按钮来添加文件或文件夹")
            self.info_label.setStyleSheet("")

            urls = event.mimeData().urls()
            if urls:
                paths = []
                for url in urls:
                    if url.isLocalFile():
                        path = url.toLocalFile()
                        paths.append(path)

                if paths:
                    self.log_message(f"检测到拖拽文件，共 {len(paths)} 个项目")

                    # 如果只有一个文件，直接设置到输入框
                    if len(paths) == 1:
                        self.path_input.setText(paths[0])
                        self.log_message(f"已设置路径: {paths[0]}")
                    else:
                        # 多个文件，逐个添加
                        success_count = 0
                        for path in paths:
                            self.log_message(f"正在添加: {os.path.basename(path)}")
                            if self.add_exclusion(path):
                                success_count += 1

                        self.log_message(f"拖拽添加完成，成功添加 {success_count}/{len(paths)} 个项目")

                    event.acceptProposedAction()
                else:
                    self.log_message("⚠️ 拖拽的项目中没有有效的本地文件")
                    event.ignore()
            else:
                self.log_message("⚠️ 拖拽数据无效")
                event.ignore()

        except Exception as e:
            self.log_message(f"处理拖拽事件时发生错误: {str(e)}")
            event.ignore()

    def show_input_context_menu(self, position):
        """显示输入框右键菜单"""
        menu = QMenu(self)

        # 标准编辑操作
        if self.path_input.hasSelectedText():
            cut_action = QAction("✂️ 剪切", self)
            cut_action.triggered.connect(self.path_input.cut)
            menu.addAction(cut_action)

            copy_action = QAction("📋 复制", self)
            copy_action.triggered.connect(self.path_input.copy)
            menu.addAction(copy_action)

        paste_action = QAction("📄 粘贴", self)
        paste_action.triggered.connect(self.path_input.paste)
        menu.addAction(paste_action)

        # 智能粘贴（自动去除引号）
        smart_paste_action = QAction("🧠 智能粘贴（去引号）", self)
        smart_paste_action.triggered.connect(self.smart_paste)
        menu.addAction(smart_paste_action)

        if self.path_input.text():
            clear_action = QAction("🗑️ 清空", self)
            clear_action.triggered.connect(self.path_input.clear)
            menu.addAction(clear_action)

        menu.addSeparator()

        # 选择全部
        select_all_action = QAction("🔘 全选", self)
        select_all_action.triggered.connect(self.path_input.selectAll)
        menu.addAction(select_all_action)

        menu.addSeparator()

        # 快捷路径
        quick_paths_menu = menu.addMenu("📁 快捷路径")

        # 用户文件夹
        user_folder_action = QAction("👤 用户文件夹", self)
        user_folder_action.triggered.connect(lambda: self.path_input.setText(os.path.expanduser("~")))
        quick_paths_menu.addAction(user_folder_action)

        # 桌面
        desktop_action = QAction("🖥️ 桌面", self)
        desktop_action.triggered.connect(lambda: self.path_input.setText(os.path.join(os.path.expanduser("~"), "Desktop")))
        quick_paths_menu.addAction(desktop_action)

        # 下载文件夹
        downloads_action = QAction("📥 下载文件夹", self)
        downloads_action.triggered.connect(lambda: self.path_input.setText(os.path.join(os.path.expanduser("~"), "Downloads")))
        quick_paths_menu.addAction(downloads_action)

        # 文档文件夹
        documents_action = QAction("📄 文档文件夹", self)
        documents_action.triggered.connect(lambda: self.path_input.setText(os.path.join(os.path.expanduser("~"), "Documents")))
        quick_paths_menu.addAction(documents_action)

        # 临时文件夹
        temp_action = QAction("🗂️ 临时文件夹", self)
        temp_action.triggered.connect(lambda: self.path_input.setText(os.environ.get('TEMP', 'C:\\Temp')))
        quick_paths_menu.addAction(temp_action)

        # 显示菜单
        menu.exec(self.path_input.mapToGlobal(position))

    def add_downloads_folder(self):
        """添加下载文件夹到排除项"""
        downloads_path = os.path.join(os.path.expanduser("~"), "Downloads")
        self.add_exclusion(downloads_path)

    def add_desktop_folder(self):
        """添加桌面文件夹到排除项"""
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        self.add_exclusion(desktop_path)

    def add_documents_folder(self):
        """添加文档文件夹到排除项"""
        documents_path = os.path.join(os.path.expanduser("~"), "Documents")
        self.add_exclusion(documents_path)

    def add_temp_folder(self):
        """添加临时文件夹到排除项"""
        temp_path = os.environ.get('TEMP', 'C:\\Temp')
        self.add_exclusion(temp_path)

    def add_path_to_dep_protection(self):
        """从输入框添加路径到DEP保护"""
        path = self.path_input.text().strip()
        if not path:
            self.log_message("⚠️ 请输入程序路径")
            return

        # 智能处理路径：去除引号
        path = self.clean_path(path)

        # 展开环境变量和用户目录
        path = os.path.expandvars(os.path.expanduser(path))

        if self.add_dep_protection(path):
            self.path_input.clear()

    def add_dep_protection(self, path: str):
        """添加程序到DEP例外列表（系统DEP设置）"""
        if not path:
            self.log_message("✗ 路径为空")
            return False

        if not os.path.exists(path):
            self.log_message(f"✗ 路径不存在: {path}")
            return False

        # 检查是否为可执行文件
        if not path.lower().endswith(('.exe', '.com', '.scr', '.msi')):
            self.log_message(f"⚠️ DEP保护通常用于可执行文件(.exe, .com, .scr, .msi): {path}")

        # 规范化路径
        path = os.path.abspath(path)

        # 检查是否已存在
        if path in self.current_dep_apps:
            self.log_message(f"⚠️ 程序已在DEP例外列表中: {path}")
            return False

        # 使用reg add命令直接添加到注册表（更可靠的方法）
        try:
            self.log_message(f"正在添加程序到DEP例外列表: {os.path.basename(path)}")

            # 构建reg add命令
            command = f'reg add "HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\AppCompatFlags\\Layers" /v "{path}" /t REG_SZ /d "DisableNXShowUI" /f'

            result = subprocess.run(command, shell=True,
                                  capture_output=True, text=True, encoding='gbk')

            if result.returncode == 0:
                self.log_message(f"✓ 成功添加程序到DEP例外列表: {os.path.basename(path)}")
                self.log_message("⚠️ 重要提示: DEP设置更改需要重启计算机才能生效")

                # 更新列表
                self.current_dep_apps.append(path)
                self.update_dep_list()
                self.statusBar().showMessage(f"成功添加程序到DEP例外列表: {os.path.basename(path)}", 3000)

                # 提示用户需要重启
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.information(self, "添加成功",
                    f"程序已成功添加到DEP例外列表:\n{os.path.basename(path)}\n\n"
                    "⚠️ 重要提示:\n"
                    "DEP设置更改需要重启计算机才能生效。\n"
                    "请在方便时重启计算机以应用更改。")

                return True
            else:
                error_msg = result.stderr.strip() if result.stderr else "未知错误"
                self.log_message(f"✗ 添加程序到DEP例外列表失败: {error_msg}")

                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "添加失败",
                    f"无法添加程序到DEP例外列表:\n{path}\n\n"
                    f"错误信息: {error_msg}\n\n"
                    "可能原因:\n"
                    "1. 需要管理员权限\n"
                    "2. 注册表访问被限制\n"
                    "3. 系统策略阻止修改")

                return False

        except Exception as e:
            self.log_message(f"✗ 添加DEP例外时发生异常: {str(e)}")
            return False






    def add_exclusion(self, path: str):
        """添加排除项"""
        if not path:
            self.log_message("✗ 路径为空")
            return False

        if not os.path.exists(path):
            self.log_message(f"✗ 路径不存在: {path}")
            return False

        # 规范化路径
        path = os.path.abspath(path)

        # 检查是否已存在
        if path in self.current_exclusions:
            self.log_message(f"⚠️ 排除项已存在: {path}")
            return False

        # 构建PowerShell命令
        if os.path.isdir(path):
            command = f'Add-MpPreference -ExclusionPath "{path}"'
            operation = f"添加文件夹排除项: {os.path.basename(path)}"
        else:
            command = f'Add-MpPreference -ExclusionPath "{path}"'
            operation = f"添加文件排除项: {os.path.basename(path)}"

        # 执行命令
        self.execute_powershell_command(command, operation, lambda success, output: self.on_add_complete(success, path))
        return True

    def remove_exclusion(self, path: str):
        """移除排除项"""
        if os.path.isdir(path):
            command = f'Remove-MpPreference -ExclusionPath "{path}"'
            operation = f"移除文件夹排除项: {path}"
        else:
            command = f'Remove-MpPreference -ExclusionPath "{path}"'
            operation = f"移除文件排除项: {path}"

        # 执行命令
        self.execute_powershell_command(command, operation, lambda success, output: self.on_remove_complete(success, path))

    def refresh_exclusions(self):
        """刷新排除项列表"""
        command = 'Get-MpPreference | Select-Object -ExpandProperty ExclusionPath'
        operation = "获取当前排除项列表"

        self.execute_powershell_command(command, operation, self.on_refresh_complete)

    def execute_powershell_command(self, command: str, operation: str, callback=None):
        """执行PowerShell命令"""
        try:
            # 如果有正在运行的worker，先停止它
            if self.worker and self.worker.isRunning():
                self.worker.terminate()
                self.worker.wait(1000)

            self.worker = PowerShellWorker(command, operation)
            self.worker.log_message.connect(self.log_message)

            if callback:
                self.worker.finished.connect(callback)

            self.worker.start()
        except Exception as e:
            self.log_message(f"执行PowerShell命令失败: {str(e)}")
            if callback:
                callback(False, str(e))

    def on_add_complete(self, success: bool, path: str):
        """添加完成回调"""
        if success:
            self.current_exclusions.append(path)
            self.update_exclusion_list()
            self.statusBar().showMessage(f"成功添加排除项: {os.path.basename(path)}", 3000)
        else:
            QMessageBox.warning(self, "添加失败", f"无法添加排除项:\n{path}")

    def on_remove_complete(self, success: bool, path: str):
        """移除完成回调"""
        if success:
            if path in self.current_exclusions:
                self.current_exclusions.remove(path)
            self.update_exclusion_list()
            self.statusBar().showMessage(f"成功移除排除项: {os.path.basename(path)}", 3000)
        else:
            QMessageBox.warning(self, "移除失败", f"无法移除排除项:\n{path}")

    def on_refresh_complete(self, success: bool, output: str):
        """刷新完成回调"""
        if success:
            # 解析输出
            self.current_exclusions = []
            if output.strip():
                lines = output.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line and os.path.exists(line):
                        self.current_exclusions.append(line)

            self.update_exclusion_list()
            self.statusBar().showMessage(f"已刷新，共 {len(self.current_exclusions)} 个排除项", 3000)
        else:
            QMessageBox.warning(self, "刷新失败", "无法获取排除项列表")

    def update_exclusion_list(self):
        """更新排除项列表显示"""
        self.exclusion_list.clear()

        for path in sorted(self.current_exclusions):
            item = QListWidgetItem()

            # 设置显示文本
            if os.path.isdir(path):
                display_text = f"📁 {path}"
            else:
                display_text = f"📄 {path}"

            item.setText(display_text)
            item.setData(Qt.ItemDataRole.UserRole, path)  # 存储原始路径

            # 检查路径是否存在
            if not os.path.exists(path):
                item.setForeground(Qt.GlobalColor.red)
                item.setToolTip(f"路径不存在: {path}")
            else:
                item.setToolTip(path)

            self.exclusion_list.addItem(item)

    def show_exclusion_context_menu(self, position):
        """显示排除项右键菜单"""
        item = self.exclusion_list.itemAt(position)
        if not item:
            return

        path = item.data(Qt.ItemDataRole.UserRole)

        menu = QMenu(self)

        # 移除排除项
        remove_action = QAction("🗑️ 移除排除项", self)
        remove_action.triggered.connect(lambda: self.remove_exclusion(path))
        menu.addAction(remove_action)

        menu.addSeparator()

        # 在资源管理器中打开
        if os.path.exists(path):
            if os.path.isdir(path):
                open_action = QAction("📂 在资源管理器中打开", self)
                open_action.triggered.connect(lambda: os.startfile(path))
            else:
                open_action = QAction("📂 在资源管理器中显示", self)
                open_action.triggered.connect(lambda: os.startfile(os.path.dirname(path)))
            menu.addAction(open_action)

        # 复制路径
        copy_action = QAction("📋 复制路径", self)
        copy_action.triggered.connect(lambda: QApplication.clipboard().setText(path))
        menu.addAction(copy_action)

        # 显示菜单
        menu.exec(self.exclusion_list.mapToGlobal(position))



    def refresh_dep_list(self):
        """刷新DEP保护程序列表"""
        # 使用reg query命令直接读取注册表（最可靠的方法）
        try:
            self.log_message("正在读取系统DEP设置...")

            command = 'reg query "HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\AppCompatFlags\\Layers"'

            result = subprocess.run(command, shell=True,
                                  capture_output=True, text=True, encoding='gbk')

            if result.returncode == 0 and result.stdout.strip():
                programs = []
                lines = result.stdout.strip().split('\n')

                for line in lines:
                    line = line.strip()
                    # 查找包含DisableNXShowUI的行
                    if 'DisableNXShowUI' in line and '.exe' in line:
                        # 提取程序路径（在REG_SZ之前的部分）
                        parts = line.split('REG_SZ')
                        if len(parts) >= 2:
                            program_path = parts[0].strip()
                            if program_path and program_path.endswith('.exe'):
                                programs.append(program_path)

                # 更新程序列表
                self.current_dep_apps = programs
                self.update_dep_list()
                self.log_message(f"✓ 成功读取DEP例外程序列表，共 {len(programs)} 个程序")
                self.statusBar().showMessage(f"已刷新DEP保护程序，共 {len(programs)} 个程序", 3000)

                # 显示找到的程序
                if programs:
                    self.log_message("找到的DEP例外程序:")
                    for i, prog in enumerate(programs, 1):
                        self.log_message(f"  {i}. {os.path.basename(prog)}")
                else:
                    self.log_message("未找到任何DEP例外程序")

            else:
                self.log_message("未找到DEP例外程序或注册表项不存在")
                self.current_dep_apps = []
                self.update_dep_list()

        except Exception as e:
            self.log_message(f"读取DEP设置失败: {str(e)}")
            self.current_dep_apps = []
            self.update_dep_list()




    def update_dep_list(self):
        """更新DEP程序列表显示"""
        self.dep_list.clear()

        for path in sorted(self.current_dep_apps):
            item = QListWidgetItem()

            # 设置显示文本
            display_text = f"🔒 {os.path.basename(path)}"

            item.setText(display_text)
            item.setData(Qt.ItemDataRole.UserRole, path)  # 存储原始路径

            # 检查路径是否存在
            if not os.path.exists(path):
                item.setForeground(Qt.GlobalColor.red)
                item.setToolTip(f"程序不存在: {path}")
            else:
                item.setToolTip(f"DEP保护程序: {path}")

            self.dep_list.addItem(item)

    def show_dep_context_menu(self, position):
        """显示DEP程序右键菜单"""
        item = self.dep_list.itemAt(position)
        if not item:
            return

        path = item.data(Qt.ItemDataRole.UserRole)

        menu = QMenu(self)

        # 在资源管理器中显示
        if os.path.exists(path):
            open_action = QAction("📂 在资源管理器中显示", self)
            open_action.triggered.connect(lambda: os.startfile(os.path.dirname(path)))
            menu.addAction(open_action)

            # 运行程序
            if path.lower().endswith('.exe'):
                run_action = QAction("▶️ 运行程序", self)
                run_action.triggered.connect(lambda: os.startfile(path))
                menu.addAction(run_action)

        # 复制路径
        copy_action = QAction("📋 复制路径", self)
        copy_action.triggered.connect(lambda: QApplication.clipboard().setText(path))
        menu.addAction(copy_action)

        menu.addSeparator()

        # 查看DEP设置
        dep_info_action = QAction("ℹ️ 查看DEP设置", self)
        dep_info_action.triggered.connect(lambda: self.show_dep_info(path))
        menu.addAction(dep_info_action)

        # 删除DEP例外
        remove_action = QAction("🗑️ 从DEP例外列表中移除", self)
        remove_action.triggered.connect(lambda: self.remove_dep_protection(path))
        menu.addAction(remove_action)

        # 显示菜单
        menu.exec(self.dep_list.mapToGlobal(position))

    def show_dep_info(self, path):
        """显示DEP设置信息"""
        program_name = os.path.basename(path)

        info_text = f"""DEP (数据执行保护) 例外程序信息

程序: {program_name}
路径: {path}

DEP状态: 例外程序（已禁用DEP保护）
设置位置: 系统属性 → 高级 → 性能设置 → 数据执行保护

说明:
- 此程序在系统DEP例外列表中
- 对此程序禁用了DEP内存保护
- 这是通过"为除下列选定程序外的所有程序和服务启用DEP"设置实现的
- 修改DEP设置需要重启计算机才能生效

访问系统DEP设置:
1. 右键"此电脑" → 属性
2. 高级系统设置 → 高级 → 性能设置
3. 数据执行保护选项卡"""

        QMessageBox.information(self, "DEP例外程序信息", info_text)

    def remove_dep_protection(self, path: str):
        """从DEP例外列表中移除程序"""
        if not path:
            self.log_message("✗ 路径为空")
            return False

        # 确认删除
        from PyQt6.QtWidgets import QMessageBox
        reply = QMessageBox.question(self, "确认删除",
            f"确定要从DEP例外列表中移除以下程序吗？\n\n"
            f"程序: {os.path.basename(path)}\n"
            f"路径: {path}\n\n"
            f"移除后，该程序将重新受到DEP保护。\n"
            f"更改需要重启计算机才能生效。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No)

        if reply != QMessageBox.StandardButton.Yes:
            return False

        try:
            self.log_message(f"正在从DEP例外列表中移除程序: {os.path.basename(path)}")

            # 使用reg delete命令删除注册表项
            command = f'reg delete "HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\AppCompatFlags\\Layers" /v "{path}" /f'

            result = subprocess.run(command, shell=True,
                                  capture_output=True, text=True, encoding='gbk')

            if result.returncode == 0:
                self.log_message(f"✓ 成功从DEP例外列表中移除程序: {os.path.basename(path)}")
                self.log_message("⚠️ 重要提示: DEP设置更改需要重启计算机才能生效")

                # 更新列表
                if path in self.current_dep_apps:
                    self.current_dep_apps.remove(path)
                self.update_dep_list()
                self.statusBar().showMessage(f"成功从DEP例外列表中移除程序: {os.path.basename(path)}", 3000)

                # 提示用户需要重启
                QMessageBox.information(self, "移除成功",
                    f"程序已成功从DEP例外列表中移除:\n{os.path.basename(path)}\n\n"
                    "⚠️ 重要提示:\n"
                    "DEP设置更改需要重启计算机才能生效。\n"
                    "重启后，该程序将重新受到DEP保护。")

                return True
            else:
                error_msg = result.stderr.strip() if result.stderr else "未知错误"
                self.log_message(f"✗ 从DEP例外列表中移除程序失败: {error_msg}")

                QMessageBox.warning(self, "移除失败",
                    f"无法从DEP例外列表中移除程序:\n{path}\n\n"
                    f"错误信息: {error_msg}\n\n"
                    "可能原因:\n"
                    "1. 需要管理员权限\n"
                    "2. 注册表访问被限制\n"
                    "3. 该程序不在DEP例外列表中")

                return False

        except Exception as e:
            self.log_message(f"✗ 移除DEP例外时发生异常: {str(e)}")
            QMessageBox.critical(self, "错误", f"移除DEP例外时发生异常:\n{str(e)}")
            return False


def main():
    """主函数"""
    try:
        app = QApplication(sys.argv)
        app.setApplicationName("Windows安全中心排除项管理器")
        app.setApplicationVersion("1.0")

        # 设置应用图标
        icon_path = os.path.join(os.path.dirname(__file__), "图标.ico")
        if os.path.exists(icon_path):
            app.setWindowIcon(QIcon(icon_path))

        window = DefenderExclusionManager()
        window.show()

        # 确保窗口显示
        app.processEvents()

        sys.exit(app.exec())

    except Exception as e:
        # 如果出现异常，显示错误信息
        try:
            from PyQt6.QtWidgets import QMessageBox
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Icon.Critical)
            msg.setWindowTitle("启动错误")
            msg.setText(f"程序启动失败:\n{str(e)}")
            msg.setDetailedText(f"错误详情:\n{str(e)}")
            msg.exec()
        except:
            # 如果连错误对话框都无法显示，则输出到控制台
            print(f"程序启动失败: {e}")
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
