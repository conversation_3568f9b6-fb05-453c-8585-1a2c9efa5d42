
import os
import time
import hashlib
import base64
import requests
import json
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend
import threading
import sys
import tkinter as tk
from tkinter import simpledialog, messagebox, ttk

def get_application_path():
    """获取应用程序所在目录，兼容打包后的可执行文件"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        return os.path.dirname(sys.executable)
    else:
        # 如果是源代码运行
        return os.path.dirname(os.path.abspath(__file__))

def clear_line():
    """清除当前行并回到行首"""
    print("\r", end="", flush=True)

def show_countdown_window(seconds: int, reason: str):
    """显示GUI倒计时窗口"""
    root = tk.Tk()
    root.title("程序即将退出")
    root.geometry("500x300")
    root.resizable(False, False)
    root.configure(bg="#ffebee")
    root.eval('tk::PlaceWindow . center')

    # 设置窗口置顶
    root.attributes('-topmost', True)
    root.focus_force()

    # 图标标签
    icon_label = tk.Label(
        root,
        text="⚠️",
        font=("Microsoft YaHei", 48),
        bg="#ffebee",
        fg="#d32f2f"
    )
    icon_label.pack(pady=20)

    # 原因标签
    reason_label = tk.Label(
        root,
        text=reason,
        font=("Microsoft YaHei", 14, "bold"),
        bg="#ffebee",
        fg="#d32f2f"
    )
    reason_label.pack(pady=10)

    # 倒计时标签
    countdown_label = tk.Label(
        root,
        text=f"程序将在 {seconds} 秒后自动退出",
        font=("Microsoft YaHei", 16),
        bg="#ffebee",
        fg="#333333"
    )
    countdown_label.pack(pady=20)

    # 进度条
    progress = ttk.Progressbar(
        root,
        length=400,
        mode='determinate',
        maximum=seconds
    )
    progress.pack(pady=10)
    progress['value'] = seconds

    # 按钮框架
    button_frame = tk.Frame(root, bg="#ffebee")
    button_frame.pack(pady=20)

    # 立即退出按钮
    def force_exit():
        root.destroy()
        os._exit(1)

    exit_btn = tk.Button(
        button_frame,
        text="立即退出",
        command=force_exit,
        font=("Microsoft YaHei", 12),
        width=12,
        bg="#d32f2f",
        fg="white",
        relief="flat"
    )
    exit_btn.pack(side=tk.LEFT, padx=10)

    # 倒计时更新函数
    def update_countdown():
        nonlocal seconds
        if seconds > 0:
            countdown_label.config(text=f"程序将在 {seconds} 秒后自动退出")
            progress['value'] = seconds
            seconds -= 1
            root.after(1000, update_countdown)
        else:
            root.destroy()
            os._exit(1)

    # 开始倒计时
    root.after(1000, update_countdown)

    # 绑定关闭事件
    root.protocol("WM_DELETE_WINDOW", force_exit)

    try:
        root.mainloop()
    except:
        os._exit(1)

def create_popup_menu(widget):
    """为控件创建右键菜单（复制、粘贴功能）"""
    popup = tk.Menu(widget, tearoff=0)
    popup.add_command(label="复制", command=lambda: widget.event_generate('<<Copy>>'))
    popup.add_command(label="粘贴", command=lambda: widget.event_generate('<<Paste>>'))
    popup.add_separator()
    popup.add_command(label="全选", command=lambda: widget.select_range(0, 'end'))
    
    def show_menu(event):
        popup.post(event.x_root, event.y_root)
    
    widget.bind("<Button-3>", show_menu)
    return popup

def get_license_key():
    """显示GUI输入框获取卡密"""
    root = tk.Tk()
    root.title("卡密验证")
    root.geometry("600x200")
    root.resizable(True, False)
    root.configure(bg="#f0f0f0")
    root.eval('tk::PlaceWindow . center')
    
    label = tk.Label(
        root, 
        text="请输入您的卡密:", 
        font=("Microsoft YaHei", 12),
        bg="#f0f0f0",
        pady=20
    )
    label.pack()
    
    entry_var = tk.StringVar()
    entry = tk.Entry(
        root, 
        textvariable=entry_var, 
        font=("Microsoft YaHei", 12),
        width=40
    )
    entry.pack(pady=10)
    entry.focus_set()
    create_popup_menu(entry)
    
    button_frame = tk.Frame(root, bg="#f0f0f0")
    button_frame.pack(pady=20)
    
    result = [None]
    
    def on_confirm():
        result[0] = entry_var.get()
        root.destroy()
    
    def on_cancel():
        root.destroy()
    
    confirm_btn = tk.Button(
        button_frame, 
        text="确认", 
        command=on_confirm,
        font=("Microsoft YaHei", 10),
        width=10,
        bg="#4CAF50",
        fg="white"
    )
    confirm_btn.pack(side=tk.LEFT, padx=10)
    
    cancel_btn = tk.Button(
        button_frame, 
        text="取消", 
        command=on_cancel,
        font=("Microsoft YaHei", 10),
        width=10,
        bg="#f44336",
        fg="white"
    )
    cancel_btn.pack(side=tk.LEFT, padx=10)
    
    root.bind('<Return>', lambda event: on_confirm())
    root.mainloop()
    
    return result[0]

def save_license_key(license_key):
    """保存卡密到文件"""
    try:
        script_dir = get_application_path()
        license_file = os.path.join(script_dir, ".license")
        
        with open(license_file, "w") as f:
            f.write(license_key)
            
        return True
    except Exception as e:
        print(f"保存卡密文件失败: {e}")
        return False

class LicenseValidator:
    """卡密验证器，用于验证卡密有效性并检查是否过期"""
    
    def __init__(self, master_key: str):
        self.master_key = master_key.encode('utf-8')
        self.aes_key = hashlib.sha256(self.master_key).digest()[:16]
    
    def _unformat_license_key(self, license_key: str) -> str:
        cleaned_key = ''.join(filter(lambda c: c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=', license_key))
        
        if len(cleaned_key) < 15:
            raise ValueError("卡密长度不足")
        
        prefix = cleaned_key[:3]
        checksum = cleaned_key[-4:]
        encrypted = cleaned_key[3:-4]
        
        if not (prefix.startswith('L') and len(prefix) == 3 and prefix[1:].isdigit()):
            raise ValueError("卡密前缀格式不正确")
        
        checksum_data = f"{prefix}{encrypted}"
        expected_checksum = hashlib.md5(checksum_data.encode('utf-8')).hexdigest()[:4]
        
        if checksum != expected_checksum:
            raise ValueError(f"卡密校验失败，预期: {expected_checksum}, 实际: {checksum}")
        
        missing_padding = len(encrypted) % 4
        if missing_padding:
            encrypted += '=' * (4 - missing_padding)
        
        return encrypted
    
    def _unpad_data(self, padded_data: bytes) -> bytes:
        unpadder = padding.PKCS7(128).unpadder()
        return unpadder.update(padded_data) + unpadder.finalize()
    
    def _decrypt(self, ciphertext: str) -> str:
        try:
            encrypted_data = base64.b64decode(ciphertext)
            
            if len(encrypted_data) < 16:
                raise ValueError(f"加密数据长度过短: {len(encrypted_data)}")
            if len(encrypted_data) % 16 != 0:
                raise ValueError(f"加密数据长度不是16的倍数: {len(encrypted_data)}")
            
            iv = encrypted_data[:16]
            ciphertext = encrypted_data[16:]
            
            cipher = Cipher(algorithms.AES(self.aes_key), modes.CBC(iv), backend=default_backend())
            decryptor = cipher.decryptor()
            
            decrypted_padded = decryptor.update(ciphertext) + decryptor.finalize()
            decrypted = self._unpad_data(decrypted_padded)
            
            return decrypted.decode('utf-8')
        
        except Exception as e:
            raise ValueError(f"解密失败: {e}")
    
    def get_network_time(self) -> int:
        try:
            response = requests.get("https://f.m.suning.com/api/ct.do", timeout=3)
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == "1" and "currentTime" in data:
                    return int(data["currentTime"]) // 1000
        except Exception:
            pass
        
        local_time = int(time.time())
        if time.localtime(local_time).tm_gmtoff // 3600 != 8:
            local_time += 8 * 3600
        return local_time
    
    def format_remaining_time(self, remaining_seconds: int) -> str:
        if remaining_seconds < 0:
            return "已过期"

        # 如果剩余时间小于1分钟，显示秒数
        if remaining_seconds < 60:
            return f"{remaining_seconds}秒"

        # 如果剩余时间小于1小时，显示分钟和秒数
        if remaining_seconds < 3600:
            minutes, seconds = divmod(remaining_seconds, 60)
            return f"{minutes}分钟{seconds}秒"

        hours, remainder = divmod(remaining_seconds, 3600)
        minutes = remainder // 60

        if hours >= 24:
            days = hours // 24
            hours = hours % 24
            return f"{days}天{hours}小时{minutes}分钟"
        else:
            return f"{hours}小时{minutes}分钟"
    
    def verify_license_key(self, license_key: str, expected_product_id: str = None) -> dict:
        result = {
            "valid": False,
            "expired": False,
            "remaining_seconds": 0,
            "remaining_time_formatted": "",
            "message": "",
            "data": None
        }
        
        try:
            encrypted_data = self._unformat_license_key(license_key)
            decrypted_data = self._decrypt(encrypted_data)
            license_data = json.loads(decrypted_data)
            
            signature_data = license_data.copy()
            original_signature = signature_data.pop("signature", "")
            signature_json = json.dumps(signature_data, sort_keys=True)
            calculated_signature = hashlib.sha256((signature_json + self.master_key.decode('utf-8')).encode('utf-8')).hexdigest()[:8]
            
            if original_signature != calculated_signature:
                result["message"] = "卡密签名验证失败"
                return result
            
            if expected_product_id is not None and license_data["product_id"] != expected_product_id:
                result["message"] = f"卡密产品ID不匹配，预期: {expected_product_id}，实际: {license_data['product_id']}"
                return result
            
            current_time = self.get_network_time()
            
            if current_time > license_data["expire_time"]:
                result["expired"] = True
                result["message"] = "卡密已过期"
            else:
                result["valid"] = True
                result["remaining_seconds"] = license_data["expire_time"] - current_time
                result["remaining_time_formatted"] = self.format_remaining_time(result["remaining_seconds"])
                result["message"] = "卡密有效"
            
            result["data"] = license_data
            
        except Exception as e:
            result["message"] = f"验证错误: {str(e)}"
        
        return result

class LicenseManager:
    """许可证管理器，处理许可证验证和管理"""
    
    def __init__(self, product_id: str, master_key: str):
        self.product_id = product_id
        self.validator = LicenseValidator(master_key)
        self.license_data = None
        self.license_expired = False
        self.script_dir = get_application_path()
        self.license_file = os.path.join(self.script_dir, ".license")
    
    def verify_license(self) -> bool:
        try:
            # 如果许可证文件不存在，提示用户输入卡密
            if not os.path.exists(self.license_file):
                print("未找到许可证文件，请输入卡密进行验证...")
                
                while True:
                    license_key = get_license_key()
                    
                    if license_key is None:
                        print("用户取消了验证，程序退出")
                        return False
                    
                    if not license_key.strip():
                        messagebox.showerror("验证失败", "卡密不能为空!")
                        continue
                    
                    result = self.validator.verify_license_key(license_key, self.product_id)
                    
                    if result["valid"]:
                        self.license_data = result["data"]
                        
                        if save_license_key(license_key):
                            messagebox.showinfo("验证成功", f"卡密验证成功!剩余时间: {result['remaining_time_formatted']}")
                            return True
                        else:
                            messagebox.showerror("保存失败", "无法保存卡密文件，请检查写入权限")
                    else:
                        messagebox.showerror("验证失败", f"卡密无效: {result['message']}")
            else:
                with open(self.license_file, "r") as f:
                    license_key = f.read().strip()
                
                result = self.validator.verify_license_key(license_key, self.product_id)
                
                if not result["valid"]:
                    print(f"错误: 许可证无效 - {result['message']}")
                    try:
                        os.remove(self.license_file)
                    except Exception as e:
                        print(f"删除无效许可证文件失败: {e}")
                    return False
                    
                if result["expired"]:
                    print("错误: 许可证已过期")
                    try:
                        os.remove(self.license_file)
                    except Exception as e:
                        print(f"删除过期许可证文件失败: {e}")
                    return False
                    
                self.license_data = result["data"]
                return True
                
        except Exception as e:
            print(f"验证许可证时出错: {e}")
            return False
    
    def check_license_periodically(self):
        check_interval = 60  # 每分钟检查一次
        warning_shown = False

        while True:
            time.sleep(check_interval)

            try:
                # 检查许可证状态
                if os.path.exists(self.license_file):
                    with open(self.license_file, "r") as f:
                        license_key = f.read().strip()

                    result = self.validator.verify_license_key(license_key, self.product_id)

                    if result["valid"]:
                        remaining_seconds = result["remaining_seconds"]

                        # 如果剩余时间少于等于10秒，直接显示GUI倒计时
                        if remaining_seconds <= 10:
                            print(f"\n🚨 许可证即将过期！剩余时间: {remaining_seconds}秒")
                            print("显示最后倒计时窗口...")
                            try:
                                show_countdown_window(remaining_seconds, "许可证即将过期")
                            except Exception as e:
                                print(f"显示倒计时窗口失败: {e}")
                                # 如果GUI失败，强制退出
                                os._exit(1)
                            return

                        # 如果剩余时间少于5分钟，显示警告
                        if remaining_seconds <= 300 and not warning_shown:
                            warning_shown = True
                            remaining_time = self.validator.format_remaining_time(remaining_seconds)
                            print(f"\n⚠️  警告: 许可证即将过期！剩余时间: {remaining_time}")

                        # 如果剩余时间少于1分钟，每5秒检查一次
                        if remaining_seconds <= 60:
                            check_interval = 5
                            remaining_time = self.validator.format_remaining_time(remaining_seconds)
                            print(f"\n⏰ 许可证即将过期！剩余时间: {remaining_time}")

                        continue

                    # 许可证无效或过期
                    if result["expired"]:
                        self._handle_license_expiry("许可证已过期")
                    else:
                        self._handle_license_expiry(f"许可证无效: {result['message']}")
                else:
                    self._handle_license_expiry("许可证文件丢失")

            except Exception as e:
                self._handle_license_expiry(f"检查许可证时出错: {e}")

    def _handle_license_expiry(self, reason: str):
        """处理许可证过期或无效的情况"""
        self.license_expired = True

        print(f"\n❌ {reason}")

        # 删除无效的许可证文件
        try:
            if os.path.exists(self.license_file):
                os.remove(self.license_file)
                print("已删除无效的许可证文件")
        except Exception as e:
            print(f"删除许可证文件失败: {e}")

        # 倒计时退出
        countdown_seconds = 30
        print(f"\n程序将在 {countdown_seconds} 秒后自动退出...")

        # 前20秒在控制台显示倒计时
        for i in range(countdown_seconds, 10, -1):
            clear_line()
            print(f"⏱️  退出倒计时: {i} 秒 (按 Ctrl+C 立即退出)", end="", flush=True)
            time.sleep(1)

        clear_line()
        print("\n最后10秒，弹出倒计时窗口...")

        # 最后10秒显示GUI倒计时窗口
        try:
            show_countdown_window(10, reason)
        except Exception as e:
            print(f"显示倒计时窗口失败: {e}")
            # 如果GUI失败，继续控制台倒计时
            for i in range(10, 0, -1):
                clear_line()
                print(f"⏱️  退出倒计时: {i} 秒", end="", flush=True)
                time.sleep(1)

        # 强制退出程序
        clear_line()
        print("\n程序已关闭")
        os._exit(1)

# 初始化许可证管理器
_license_manager = LicenseManager("1", "YourSecretMasterKey1234567890")

# 初始验证
if __name__ == "__main__":
    if not _license_manager.verify_license():
        import sys
        sys.exit(1)
    
    if _license_manager.license_data is not None:
        print(f"许可证有效，剩余时间: {_license_manager.validator.format_remaining_time(_license_manager.license_data['expire_time'] - _license_manager.validator.get_network_time())}")
    else:
        print("许可证验证成功，但无法获取许可证详细信息")
    
    # 启动定时检查线程
    _check_thread = threading.Thread(target=_license_manager.check_license_periodically, daemon=True)
    _check_thread.start()
    
    # 验证成功后不退出，继续执行程序的其他部分
    print("程序正在启动...")
    # 这里是原始程序的入口点
    # 如果你有main函数，可以在这里调用
    # main()


# ===== 原始代码 =====
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示应用程序 - 用于测试卡密保护功能
"""

import time
import random

def main():
    """主程序"""
    print("🎉 欢迎使用演示应用程序！")
    print("这是一个受卡密保护的程序示例")
    
    # 模拟程序运行
    for i in range(20):
        print(f"程序运行中... 步骤 {i+1}/20")

        # 模拟一些工作
        result = sum(random.randint(1, 100) for _ in range(1000))
        print(f"计算结果: {result}")

        time.sleep(3)  # 等待3秒
    
    print("✅ 程序执行完成！")

if __name__ == "__main__":
    main()
