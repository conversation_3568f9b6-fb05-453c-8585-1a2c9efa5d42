
import os
import time
import hashlib
import base64
import requests
import json
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend
import threading
import sys
import tkinter as tk
from tkinter import simpledialog, messagebox, ttk

def get_application_path():
    """获取应用程序所在目录，兼容打包后的可执行文件"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        return os.path.dirname(sys.executable)
    else:
        # 如果是源代码运行
        return os.path.dirname(os.path.abspath(__file__))

def clear_line():
    """清除当前行并回到行首"""
    print("\r", end="", flush=True)

def show_countdown_window(seconds: int, reason: str):
    """显示GUI倒计时窗口"""
    root = tk.Tk()
    root.title("程序即将退出")
    root.geometry("500x300")
    root.resizable(False, False)
    root.configure(bg="#ffebee")
    root.eval('tk::PlaceWindow . center')

    # 设置窗口置顶
    root.attributes('-topmost', True)
    root.focus_force()

    # 图标标签
    icon_label = tk.Label(
        root,
        text="⚠️",
        font=("Microsoft YaHei", 48),
        bg="#ffebee",
        fg="#d32f2f"
    )
    icon_label.pack(pady=20)

    # 原因标签
    reason_label = tk.Label(
        root,
        text=reason,
        font=("Microsoft YaHei", 14, "bold"),
        bg="#ffebee",
        fg="#d32f2f"
    )
    reason_label.pack(pady=10)

    # 倒计时标签
    countdown_label = tk.Label(
        root,
        text=f"程序将在 {seconds} 秒后自动退出",
        font=("Microsoft YaHei", 16),
        bg="#ffebee",
        fg="#333333"
    )
    countdown_label.pack(pady=20)

    # 进度条
    progress = ttk.Progressbar(
        root,
        length=400,
        mode='determinate',
        maximum=seconds
    )
    progress.pack(pady=10)
    progress['value'] = seconds

    # 按钮框架
    button_frame = tk.Frame(root, bg="#ffebee")
    button_frame.pack(pady=20)

    # 立即退出按钮
    def force_exit():
        root.destroy()
        os._exit(1)

    exit_btn = tk.Button(
        button_frame,
        text="立即退出",
        command=force_exit,
        font=("Microsoft YaHei", 12),
        width=12,
        bg="#d32f2f",
        fg="white",
        relief="flat"
    )
    exit_btn.pack(side=tk.LEFT, padx=10)

    # 倒计时更新函数
    def update_countdown():
        nonlocal seconds
        if seconds > 0:
            countdown_label.config(text=f"程序将在 {seconds} 秒后自动退出")
            progress['value'] = seconds
            seconds -= 1
            root.after(1000, update_countdown)
        else:
            root.destroy()
            os._exit(1)

    # 开始倒计时
    root.after(1000, update_countdown)

    # 绑定关闭事件
    root.protocol("WM_DELETE_WINDOW", force_exit)

    try:
        root.mainloop()
    except:
        os._exit(1)

def create_popup_menu(widget):
    """为控件创建右键菜单（复制、粘贴功能）"""
    popup = tk.Menu(widget, tearoff=0)
    popup.add_command(label="复制", command=lambda: widget.event_generate('<<Copy>>'))
    popup.add_command(label="粘贴", command=lambda: widget.event_generate('<<Paste>>'))
    popup.add_separator()
    popup.add_command(label="全选", command=lambda: widget.select_range(0, 'end'))
    
    def show_menu(event):
        popup.post(event.x_root, event.y_root)
    
    widget.bind("<Button-3>", show_menu)
    return popup

def get_license_key():
    """显示GUI输入框获取卡密"""
    root = tk.Tk()
    root.title("卡密验证")
    root.geometry("600x200")
    root.resizable(True, False)
    root.configure(bg="#f0f0f0")
    root.eval('tk::PlaceWindow . center')
    
    label = tk.Label(
        root, 
        text="请输入您的卡密:", 
        font=("Microsoft YaHei", 12),
        bg="#f0f0f0",
        pady=20
    )
    label.pack()
    
    entry_var = tk.StringVar()
    entry = tk.Entry(
        root, 
        textvariable=entry_var, 
        font=("Microsoft YaHei", 12),
        width=40
    )
    entry.pack(pady=10)
    entry.focus_set()
    create_popup_menu(entry)
    
    button_frame = tk.Frame(root, bg="#f0f0f0")
    button_frame.pack(pady=20)
    
    result = [None]
    
    def on_confirm():
        result[0] = entry_var.get()
        root.destroy()
    
    def on_cancel():
        root.destroy()
    
    confirm_btn = tk.Button(
        button_frame, 
        text="确认", 
        command=on_confirm,
        font=("Microsoft YaHei", 10),
        width=10,
        bg="#4CAF50",
        fg="white"
    )
    confirm_btn.pack(side=tk.LEFT, padx=10)
    
    cancel_btn = tk.Button(
        button_frame, 
        text="取消", 
        command=on_cancel,
        font=("Microsoft YaHei", 10),
        width=10,
        bg="#f44336",
        fg="white"
    )
    cancel_btn.pack(side=tk.LEFT, padx=10)
    
    root.bind('<Return>', lambda event: on_confirm())
    root.mainloop()
    
    return result[0]

def save_license_key(license_key):
    """保存卡密到文件"""
    try:
        script_dir = get_application_path()
        license_file = os.path.join(script_dir, ".license")
        
        with open(license_file, "w") as f:
            f.write(license_key)
            
        return True
    except Exception as e:
        print(f"保存卡密文件失败: {e}")
        return False

class LicenseValidator:
    """卡密验证器，用于验证卡密有效性并检查是否过期"""
    
    def __init__(self, master_key: str):
        self.master_key = master_key.encode('utf-8')
        self.aes_key = hashlib.sha256(self.master_key).digest()[:16]
    
    def _unformat_license_key(self, license_key: str) -> str:
        cleaned_key = ''.join(filter(lambda c: c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=', license_key))
        
        if len(cleaned_key) < 15:
            raise ValueError("卡密长度不足")
        
        prefix = cleaned_key[:3]
        checksum = cleaned_key[-4:]
        encrypted = cleaned_key[3:-4]
        
        if not (prefix.startswith('L') and len(prefix) == 3 and prefix[1:].isdigit()):
            raise ValueError("卡密前缀格式不正确")
        
        checksum_data = f"{prefix}{encrypted}"
        expected_checksum = hashlib.md5(checksum_data.encode('utf-8')).hexdigest()[:4]
        
        if checksum != expected_checksum:
            raise ValueError(f"卡密校验失败，预期: {expected_checksum}, 实际: {checksum}")
        
        missing_padding = len(encrypted) % 4
        if missing_padding:
            encrypted += '=' * (4 - missing_padding)
        
        return encrypted
    
    def _unpad_data(self, padded_data: bytes) -> bytes:
        unpadder = padding.PKCS7(128).unpadder()
        return unpadder.update(padded_data) + unpadder.finalize()
    
    def _decrypt(self, ciphertext: str) -> str:
        try:
            encrypted_data = base64.b64decode(ciphertext)
            
            if len(encrypted_data) < 16:
                raise ValueError(f"加密数据长度过短: {len(encrypted_data)}")
            if len(encrypted_data) % 16 != 0:
                raise ValueError(f"加密数据长度不是16的倍数: {len(encrypted_data)}")
            
            iv = encrypted_data[:16]
            ciphertext = encrypted_data[16:]
            
            cipher = Cipher(algorithms.AES(self.aes_key), modes.CBC(iv), backend=default_backend())
            decryptor = cipher.decryptor()
            
            decrypted_padded = decryptor.update(ciphertext) + decryptor.finalize()
            decrypted = self._unpad_data(decrypted_padded)
            
            return decrypted.decode('utf-8')
        
        except Exception as e:
            raise ValueError(f"解密失败: {e}")
    
    def get_network_time(self) -> int:
        try:
            response = requests.get("https://f.m.suning.com/api/ct.do", timeout=3)
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == "1" and "currentTime" in data:
                    return int(data["currentTime"]) // 1000
        except Exception:
            pass
        
        local_time = int(time.time())
        if time.localtime(local_time).tm_gmtoff // 3600 != 8:
            local_time += 8 * 3600
        return local_time
    
    def format_remaining_time(self, remaining_seconds: int) -> str:
        if remaining_seconds < 0:
            return "已过期"

        # 如果剩余时间小于1分钟，显示秒数
        if remaining_seconds < 60:
            return f"{remaining_seconds}秒"

        # 如果剩余时间小于1小时，显示分钟和秒数
        if remaining_seconds < 3600:
            minutes, seconds = divmod(remaining_seconds, 60)
            return f"{minutes}分钟{seconds}秒"

        hours, remainder = divmod(remaining_seconds, 3600)
        minutes = remainder // 60

        if hours >= 24:
            days = hours // 24
            hours = hours % 24
            return f"{days}天{hours}小时{minutes}分钟"
        else:
            return f"{hours}小时{minutes}分钟"
    
    def verify_license_key(self, license_key: str, expected_product_id: str = None) -> dict:
        result = {
            "valid": False,
            "expired": False,
            "remaining_seconds": 0,
            "remaining_time_formatted": "",
            "message": "",
            "data": None
        }
        
        try:
            encrypted_data = self._unformat_license_key(license_key)
            decrypted_data = self._decrypt(encrypted_data)
            license_data = json.loads(decrypted_data)
            
            signature_data = license_data.copy()
            original_signature = signature_data.pop("signature", "")
            signature_json = json.dumps(signature_data, sort_keys=True)
            calculated_signature = hashlib.sha256((signature_json + self.master_key.decode('utf-8')).encode('utf-8')).hexdigest()[:8]
            
            if original_signature != calculated_signature:
                result["message"] = "卡密签名验证失败"
                return result
            
            if expected_product_id is not None and license_data["product_id"] != expected_product_id:
                result["message"] = f"卡密产品ID不匹配，预期: {expected_product_id}，实际: {license_data['product_id']}"
                return result
            
            current_time = self.get_network_time()
            
            if current_time > license_data["expire_time"]:
                result["expired"] = True
                result["message"] = "卡密已过期"
            else:
                result["valid"] = True
                result["remaining_seconds"] = license_data["expire_time"] - current_time
                result["remaining_time_formatted"] = self.format_remaining_time(result["remaining_seconds"])
                result["message"] = "卡密有效"
            
            result["data"] = license_data
            
        except Exception as e:
            result["message"] = f"验证错误: {str(e)}"
        
        return result

class LicenseManager:
    """许可证管理器，处理许可证验证和管理"""
    
    def __init__(self, product_id: str, master_key: str):
        self.product_id = product_id
        self.validator = LicenseValidator(master_key)
        self.license_data = None
        self.license_expired = False
        self.script_dir = get_application_path()
        self.license_file = os.path.join(self.script_dir, ".license")
    
    def verify_license(self) -> bool:
        try:
            # 如果许可证文件不存在，提示用户输入卡密
            if not os.path.exists(self.license_file):
                print("未找到许可证文件，请输入卡密进行验证...")
                
                while True:
                    license_key = get_license_key()
                    
                    if license_key is None:
                        print("用户取消了验证，程序退出")
                        return False
                    
                    if not license_key.strip():
                        messagebox.showerror("验证失败", "卡密不能为空!")
                        continue
                    
                    result = self.validator.verify_license_key(license_key, self.product_id)
                    
                    if result["valid"]:
                        self.license_data = result["data"]
                        
                        if save_license_key(license_key):
                            messagebox.showinfo("验证成功", f"卡密验证成功!剩余时间: {result['remaining_time_formatted']}")
                            return True
                        else:
                            messagebox.showerror("保存失败", "无法保存卡密文件，请检查写入权限")
                    else:
                        messagebox.showerror("验证失败", f"卡密无效: {result['message']}")
            else:
                with open(self.license_file, "r") as f:
                    license_key = f.read().strip()
                
                result = self.validator.verify_license_key(license_key, self.product_id)
                
                if not result["valid"]:
                    print(f"错误: 许可证无效 - {result['message']}")
                    try:
                        os.remove(self.license_file)
                    except Exception as e:
                        print(f"删除无效许可证文件失败: {e}")
                    return False
                    
                if result["expired"]:
                    print("错误: 许可证已过期")
                    try:
                        os.remove(self.license_file)
                    except Exception as e:
                        print(f"删除过期许可证文件失败: {e}")
                    return False
                    
                self.license_data = result["data"]
                return True
                
        except Exception as e:
            print(f"验证许可证时出错: {e}")
            return False
    
    def check_license_periodically(self):
        check_interval = 60  # 每分钟检查一次
        warning_shown = False

        while True:
            time.sleep(check_interval)

            try:
                # 检查许可证状态
                if os.path.exists(self.license_file):
                    with open(self.license_file, "r") as f:
                        license_key = f.read().strip()

                    result = self.validator.verify_license_key(license_key, self.product_id)

                    if result["valid"]:
                        remaining_seconds = result["remaining_seconds"]

                        # 如果剩余时间少于等于10秒，直接显示GUI倒计时
                        if remaining_seconds <= 10:
                            print(f"\n🚨 许可证即将过期！剩余时间: {remaining_seconds}秒")
                            print("显示最后倒计时窗口...")
                            try:
                                show_countdown_window(remaining_seconds, "许可证即将过期")
                            except Exception as e:
                                print(f"显示倒计时窗口失败: {e}")
                                # 如果GUI失败，强制退出
                                os._exit(1)
                            return

                        # 如果剩余时间少于5分钟，显示警告
                        if remaining_seconds <= 300 and not warning_shown:
                            warning_shown = True
                            remaining_time = self.validator.format_remaining_time(remaining_seconds)
                            print(f"\n⚠️  警告: 许可证即将过期！剩余时间: {remaining_time}")

                        # 如果剩余时间少于1分钟，每5秒检查一次
                        if remaining_seconds <= 60:
                            check_interval = 5
                            remaining_time = self.validator.format_remaining_time(remaining_seconds)
                            print(f"\n⏰ 许可证即将过期！剩余时间: {remaining_time}")

                        continue

                    # 许可证无效或过期
                    if result["expired"]:
                        self._handle_license_expiry("许可证已过期")
                    else:
                        self._handle_license_expiry(f"许可证无效: {result['message']}")
                else:
                    self._handle_license_expiry("许可证文件丢失")

            except Exception as e:
                self._handle_license_expiry(f"检查许可证时出错: {e}")

    def _handle_license_expiry(self, reason: str):
        """处理许可证过期或无效的情况"""
        self.license_expired = True

        print(f"\n❌ {reason}")

        # 删除无效的许可证文件
        try:
            if os.path.exists(self.license_file):
                os.remove(self.license_file)
                print("已删除无效的许可证文件")
        except Exception as e:
            print(f"删除许可证文件失败: {e}")

        # 倒计时退出
        countdown_seconds = 30
        print(f"\n程序将在 {countdown_seconds} 秒后自动退出...")

        # 前20秒在控制台显示倒计时
        for i in range(countdown_seconds, 10, -1):
            clear_line()
            print(f"⏱️  退出倒计时: {i} 秒 (按 Ctrl+C 立即退出)", end="", flush=True)
            time.sleep(1)

        clear_line()
        print("\n最后10秒，弹出倒计时窗口...")

        # 最后10秒显示GUI倒计时窗口
        try:
            show_countdown_window(10, reason)
        except Exception as e:
            print(f"显示倒计时窗口失败: {e}")
            # 如果GUI失败，继续控制台倒计时
            for i in range(10, 0, -1):
                clear_line()
                print(f"⏱️  退出倒计时: {i} 秒", end="", flush=True)
                time.sleep(1)

        # 强制退出程序
        clear_line()
        print("\n程序已关闭")
        os._exit(1)

# 初始化许可证管理器
_license_manager = LicenseManager("1", "YourSecretMasterKey1234567890")

# 初始验证
if __name__ == "__main__":
    if not _license_manager.verify_license():
        import sys
        sys.exit(1)
    
    if _license_manager.license_data is not None:
        print(f"许可证有效，剩余时间: {_license_manager.validator.format_remaining_time(_license_manager.license_data['expire_time'] - _license_manager.validator.get_network_time())}")
    else:
        print("许可证验证成功，但无法获取许可证详细信息")
    
    # 启动定时检查线程
    _check_thread = threading.Thread(target=_license_manager.check_license_periodically, daemon=True)
    _check_thread.start()
    
    # 验证成功后不退出，继续执行程序的其他部分
    print("程序正在启动...")
    # 这里是原始程序的入口点
    # 如果你有main函数，可以在这里调用
    # main()


# ===== 原始代码 =====
import sys
import re
import os
import random
import json
from datetime import datetime
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QGridLayout, QLabel, QLineEdit, QPushButton, 
                            QTextEdit, QGroupBox, QRadioButton, QSpinBox, QFileDialog, 
                            QMessageBox, QButtonGroup, QFrame, QScrollArea, QCheckBox)
from PyQt6.QtCore import Qt, QMimeData, pyqtSignal
from PyQt6.QtGui import QDragEnterEvent, QDropEvent, QFont, QAction, QIcon  # 添加QIcon导入


    
class VideoLinkExtractor(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("视频链接提取器 - 作者：吖贤")
        self.setGeometry(100, 100, 1000, 900)
        
        # 设置窗口图标
        self.setWindowIcon(QIcon("黑盟链接提取.ico"))
        
        # 配置文件路径
        self.config_file = "config.json"
        
        # 设置接受拖拽
        self.setAcceptDrops(True)
        
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        self.setup_ui(main_layout)
        self.load_config()
        
    def setup_ui(self, main_layout):
        # 作者信息
        author_group = QGroupBox("作者信息")
        author_layout = QVBoxLayout(author_group)
        author_text = "作者：吖贤 | 联系方式：jk904197932 | 公众号：抖小贤 | 主营业务：批量剪辑，批量发布，监控发布等黑科技"
        author_label = QLabel(author_text)
        author_label.setStyleSheet("color: blue;")
        author_layout.addWidget(author_label)
        main_layout.addWidget(author_group)
        
        # 文件导入区域
        file_group = QGroupBox("文件导入")
        file_layout = QHBoxLayout(file_group)
        
        self.file_button = QPushButton("选择文件")
        self.file_button.clicked.connect(self.load_file)
        file_layout.addWidget(self.file_button)
        
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setReadOnly(True)
        self.file_path_edit.setPlaceholderText("支持拖拽文件到此处")
        file_layout.addWidget(self.file_path_edit)
        
        main_layout.addWidget(file_group)
        
        # 文本输入区域
        input_group = QGroupBox("文本输入")
        input_layout = QVBoxLayout(input_group)
        
        # 生成和乱序按钮行
        input_button_layout = QHBoxLayout()
        input_button_layout.addWidget(QLabel("模拟生成:"))
        
        self.link_count_spin = QSpinBox()
        self.link_count_spin.setRange(1, 10000)
        self.link_count_spin.setValue(100)
        input_button_layout.addWidget(self.link_count_spin)
        
        input_button_layout.addWidget(QLabel("个链接"))
        
        generate_button = QPushButton("生成链接")
        generate_button.clicked.connect(self.generate_links)
        input_button_layout.addWidget(generate_button)
        
        shuffle_input_button = QPushButton("疯狂乱序")
        shuffle_input_button.clicked.connect(self.shuffle_input_text)
        input_button_layout.addWidget(shuffle_input_button)
        
        # 实时显示链接数量和当前行
        self.input_count_label = QLabel("链接数量: 0")
        self.input_count_label.setStyleSheet("color: green; font-weight: bold;")
        input_button_layout.addWidget(self.input_count_label)

        self.input_line_label = QLabel("当前行: 1")
        self.input_line_label.setStyleSheet("color: blue; font-weight: bold;")
        input_button_layout.addWidget(self.input_line_label)
        
        input_button_layout.addStretch()
        input_layout.addLayout(input_button_layout)
        
        self.input_text = QTextEdit()
        self.input_text.setMaximumHeight(150)
        self.input_text.textChanged.connect(self.update_input_count)
        self.input_text.cursorPositionChanged.connect(self.update_input_cursor_position)
        input_layout.addWidget(self.input_text)
        
        main_layout.addWidget(input_group)
        
        # 设置区域
        settings_group = QGroupBox("设置")
        settings_layout = QGridLayout(settings_group)
        
        settings_layout.addWidget(QLabel("连接符:"), 0, 0)
        self.connector_edit = QLineEdit("----")
        settings_layout.addWidget(self.connector_edit, 0, 1)
        
        settings_layout.addWidget(QLabel("保存目录:"), 0, 2)
        self.save_dir_edit = QLineEdit(os.getcwd())
        settings_layout.addWidget(self.save_dir_edit, 0, 3)
        
        save_dir_button = QPushButton("选择")
        save_dir_button.clicked.connect(self.choose_save_dir)
        settings_layout.addWidget(save_dir_button, 0, 4)
        
        main_layout.addWidget(settings_group)
        
        # 用户ID区域
        id_group = QGroupBox("用户ID设置")
        id_layout = QVBoxLayout(id_group)
        
        # ID生成和乱序按钮行
        id_button_layout = QHBoxLayout()
        id_button_layout.addWidget(QLabel("模拟生成:"))
        
        self.id_count_spin = QSpinBox()
        self.id_count_spin.setRange(1, 100)
        self.id_count_spin.setValue(5)
        id_button_layout.addWidget(self.id_count_spin)
        
        id_button_layout.addWidget(QLabel("个ID"))
        
        generate_id_button = QPushButton("生成ID")
        generate_id_button.clicked.connect(self.generate_ids)
        id_button_layout.addWidget(generate_id_button)
        
        shuffle_id_button = QPushButton("疯狂乱序")
        shuffle_id_button.clicked.connect(self.shuffle_id_text)
        id_button_layout.addWidget(shuffle_id_button)
        
        # 实时显示ID数量和当前行
        self.id_count_label = QLabel("ID数量: 0")
        self.id_count_label.setStyleSheet("color: green; font-weight: bold;")
        id_button_layout.addWidget(self.id_count_label)

        self.id_line_label = QLabel("当前行: 1")
        self.id_line_label.setStyleSheet("color: blue; font-weight: bold;")
        id_button_layout.addWidget(self.id_line_label)
        
        id_button_layout.addStretch()
        id_layout.addLayout(id_button_layout)
        
        self.id_text = QTextEdit()
        self.id_text.setMaximumHeight(80)
        self.id_text.textChanged.connect(self.update_id_count)
        self.id_text.cursorPositionChanged.connect(self.update_id_cursor_position)
        id_layout.addWidget(self.id_text)
        
        main_layout.addWidget(id_group)
        
        # 分配逻辑设置
        logic_group = QGroupBox("分配逻辑")
        logic_layout = QVBoxLayout(logic_group)
        
        # 主分配模式
        main_logic_layout = QHBoxLayout()
        self.logic_group = QButtonGroup()
        
        self.average_radio = QRadioButton("平均分配")
        self.average_radio.setChecked(True)
        self.logic_group.addButton(self.average_radio, 0)
        main_logic_layout.addWidget(self.average_radio)

        self.custom_radio = QRadioButton("自定义分配")
        self.logic_group.addButton(self.custom_radio, 1)
        main_logic_layout.addWidget(self.custom_radio)

        self.full_radio = QRadioButton("拉满分配")
        self.full_radio.setToolTip("每个用户ID都分配所有链接，适用于链接少、用户多的情况")
        self.logic_group.addButton(self.full_radio, 2)
        main_logic_layout.addWidget(self.full_radio)
        
        main_logic_layout.addStretch()
        logic_layout.addLayout(main_logic_layout)
        
        # 排序方式和间隔设置
        order_layout = QHBoxLayout()
        order_layout.addWidget(QLabel("排序方式:"))
        
        self.order_group = QButtonGroup()
        self.sequential_radio = QRadioButton("顺序")
        self.sequential_radio.setChecked(True)
        self.order_group.addButton(self.sequential_radio, 0)
        order_layout.addWidget(self.sequential_radio)
        
        self.random_radio = QRadioButton("乱序")
        self.order_group.addButton(self.random_radio, 1)
        order_layout.addWidget(self.random_radio)
        
        order_layout.addWidget(QLabel("间隔:"))
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(1, 100)
        self.interval_spin.setValue(1)
        order_layout.addWidget(self.interval_spin)
        
        order_layout.addWidget(QLabel("个链接切换ID"))
        order_layout.addStretch()
        logic_layout.addLayout(order_layout)
        
        # 自定义分配参数
        custom_group = QGroupBox("自定义分配参数")
        custom_layout = QVBoxLayout(custom_group)

        # 第一阶段
        phase1_layout = QHBoxLayout()
        phase1_layout.addWidget(QLabel("第一阶段 - 每个ID分配:"))

        self.custom_count_spin = QSpinBox()
        self.custom_count_spin.setRange(1, 1000)
        self.custom_count_spin.setValue(10)
        phase1_layout.addWidget(self.custom_count_spin)

        phase1_layout.addWidget(QLabel("个链接"))
        phase1_layout.addWidget(QLabel("间隔:"))

        self.custom_interval_spin = QSpinBox()
        self.custom_interval_spin.setRange(1, 100)
        self.custom_interval_spin.setValue(1)
        phase1_layout.addWidget(self.custom_interval_spin)

        phase1_layout.addWidget(QLabel("个链接切换ID"))
        phase1_layout.addStretch()
        custom_layout.addLayout(phase1_layout)

        # 第二阶段
        phase2_layout = QHBoxLayout()
        phase2_layout.addWidget(QLabel("第二阶段 - 剩余链接:"))

        self.second_logic_group = QButtonGroup()
        self.second_average_radio = QRadioButton("平均分配")
        self.second_average_radio.setChecked(True)
        self.second_average_radio.setToolTip("按间隔数平均分配剩余链接给所有用户")
        self.second_logic_group.addButton(self.second_average_radio, 0)
        phase2_layout.addWidget(self.second_average_radio)

        self.second_cycle_radio = QRadioButton("循环分配")
        self.second_cycle_radio.setToolTip("按间隔数循环分配剩余链接，每个用户轮流获得")
        self.second_logic_group.addButton(self.second_cycle_radio, 1)
        phase2_layout.addWidget(self.second_cycle_radio)

        phase2_layout.addWidget(QLabel("间隔:"))
        self.second_interval_spin = QSpinBox()
        self.second_interval_spin.setRange(1, 100)
        self.second_interval_spin.setValue(1)
        phase2_layout.addWidget(self.second_interval_spin)

        phase2_layout.addWidget(QLabel("个链接切换ID"))
        phase2_layout.addStretch()
        custom_layout.addLayout(phase2_layout)

        logic_layout.addWidget(custom_group)
        main_layout.addWidget(logic_group)

        # 操作按钮
        button_layout = QHBoxLayout()

        extract_button = QPushButton("提取链接")
        extract_button.clicked.connect(self.extract_links)
        button_layout.addWidget(extract_button)

        clear_button = QPushButton("清空输入")
        clear_button.clicked.connect(self.clear_input)
        button_layout.addWidget(clear_button)

        save_button = QPushButton("保存结果")
        save_button.clicked.connect(self.save_results)
        button_layout.addWidget(save_button)

        config_button = QPushButton("保存配置")
        config_button.clicked.connect(self.save_config)
        button_layout.addWidget(config_button)

        button_layout.addStretch()
        main_layout.addLayout(button_layout)

        # 结果显示区域
        result_group = QGroupBox("提取结果")
        result_layout = QVBoxLayout(result_group)

        # 结果操作按钮
        result_button_layout = QHBoxLayout()

        # 复制前几行功能
        result_button_layout.addWidget(QLabel("复制前"))
        self.copy_lines_spin = QSpinBox()
        self.copy_lines_spin.setRange(1, 1000)
        self.copy_lines_spin.setValue(10)
        result_button_layout.addWidget(self.copy_lines_spin)

        result_button_layout.addWidget(QLabel("行"))

        copy_button = QPushButton("复制")
        copy_button.clicked.connect(self.copy_first_lines)
        result_button_layout.addWidget(copy_button)

        cut_button = QPushButton("剪切")
        cut_button.clicked.connect(self.cut_first_lines)
        result_button_layout.addWidget(cut_button)

        # 结果统计和当前行
        self.result_count_label = QLabel("结果数量: 0")
        self.result_count_label.setStyleSheet("color: blue; font-weight: bold;")
        result_button_layout.addWidget(self.result_count_label)

        self.result_line_label = QLabel("当前行: 1")
        self.result_line_label.setStyleSheet("color: blue; font-weight: bold;")
        result_button_layout.addWidget(self.result_line_label)

        result_button_layout.addStretch()
        result_layout.addLayout(result_button_layout)

        self.result_text = QTextEdit()
        self.result_text.textChanged.connect(self.update_result_count)
        self.result_text.cursorPositionChanged.connect(self.update_result_cursor_position)
        result_layout.addWidget(self.result_text)

        main_layout.addWidget(result_group)

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        files = [url.toLocalFile() for url in event.mimeData().urls()]
        if files and os.path.isfile(files[0]):
            self.load_file_content(files[0])

    def update_input_count(self):
        """更新输入链接数量显示"""
        content = self.input_text.toPlainText().strip()
        if content:
            # 使用正则表达式检测实际的链接数量
            url_pattern = r'https?://[^\s\u4e00-\u9fff]+'
            links = []
            lines = content.split('\n')

            for line in lines:
                # 查找链接
                url_matches = re.findall(url_pattern, line)
                for url in url_matches:
                    # 清理链接末尾可能的标点符号（已修正正则表达式）
                    url = re.sub(r'[，。！？；：""（）【】\s]+$', '', url)
                    links.append(url)

            count = len(links)
        else:
            count = 0
        self.input_count_label.setText(f"链接数量: {count}")

    def update_id_count(self):
        """更新ID数量显示"""
        content = self.id_text.toPlainText().strip()
        if content:
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            count = len(lines)
        else:
            count = 0
        self.id_count_label.setText(f"ID数量: {count}")

    def update_result_count(self):
        """更新结果数量显示"""
        content = self.result_text.toPlainText().strip()
        if content:
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            count = len(lines)
        else:
            count = 0
        self.result_count_label.setText(f"结果数量: {count}")

    def update_input_cursor_position(self):
        """更新输入框当前行号显示"""
        cursor = self.input_text.textCursor()
        line_number = cursor.blockNumber() + 1
        self.input_line_label.setText(f"当前行: {line_number}")

    def update_id_cursor_position(self):
        """更新ID框当前行号显示"""
        cursor = self.id_text.textCursor()
        line_number = cursor.blockNumber() + 1
        self.id_line_label.setText(f"当前行: {line_number}")

    def update_result_cursor_position(self):
        """更新结果框当前行号显示"""
        cursor = self.result_text.textCursor()
        line_number = cursor.blockNumber() + 1
        self.result_line_label.setText(f"当前行: {line_number}")

    def load_file(self):
        """加载文本文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择文本文件", "", "文本文件 (*.txt);;所有文件 (*.*)"
        )
        if file_path:
            self.load_file_content(file_path)

    def load_file_content(self, file_path):
        """加载文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            self.input_text.setPlainText(content)
            self.file_path_edit.setText(file_path)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取文件失败: {str(e)}")

    def choose_save_dir(self):
        """选择保存目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择保存目录")
        if directory:
            self.save_dir_edit.setText(directory)

    def generate_links(self):
        """生成模拟链接"""
        count = self.link_count_spin.value()
        links = []
        for i in range(1, count + 1):
            random_id = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789', k=7))
            link = f"https://v.kuaishou.com/{random_id}"
            links.append(link)

        self.input_text.setPlainText('\n'.join(links))
        QMessageBox.information(self, "成功", f"已生成 {count} 个模拟链接")

    def generate_ids(self):
        """生成模拟用户ID"""
        count = self.id_count_spin.value()
        ids = []
        for i in range(count):
            user_id = str(random.randint(100000000, 999999999))
            ids.append(user_id)

        self.id_text.setPlainText('\n'.join(ids))
        QMessageBox.information(self, "成功", f"已生成 {count} 个模拟用户ID")

    def shuffle_input_text(self):
        """疯狂乱序文本输入框内容"""
        content = self.input_text.toPlainText().strip()
        if not content:
            QMessageBox.warning(self, "警告", "文本输入框为空")
            return

        lines = [line.strip() for line in content.split('\n') if line.strip()]
        if len(lines) < 2:
            QMessageBox.warning(self, "警告", "至少需要2行内容才能乱序")
            return

        random.shuffle(lines)
        self.input_text.setPlainText('\n'.join(lines))
        QMessageBox.information(self, "成功", f"已对 {len(lines)} 行内容进行乱序")

    def shuffle_id_text(self):
        """疯狂乱序用户ID框内容"""
        content = self.id_text.toPlainText().strip()
        if not content:
            QMessageBox.warning(self, "警告", "用户ID框为空")
            return

        lines = [line.strip() for line in content.split('\n') if line.strip()]
        if len(lines) < 2:
            QMessageBox.warning(self, "警告", "至少需要2个ID才能乱序")
            return

        random.shuffle(lines)
        self.id_text.setPlainText('\n'.join(lines))
        QMessageBox.information(self, "成功", f"已对 {len(lines)} 个ID进行乱序")

    def copy_first_lines(self):
        """复制前几行数据"""
        content = self.result_text.toPlainText().strip()
        if not content:
            QMessageBox.warning(self, "警告", "结果为空")
            return

        lines = content.split('\n')
        copy_count = min(self.copy_lines_spin.value(), len(lines))
        copy_text = '\n'.join(lines[:copy_count])

        clipboard = QApplication.clipboard()
        clipboard.setText(copy_text)
        QMessageBox.information(self, "成功", f"已复制前 {copy_count} 行数据到剪贴板")

    def cut_first_lines(self):
        """剪切前几行数据"""
        content = self.result_text.toPlainText().strip()
        if not content:
            QMessageBox.warning(self, "警告", "结果为空")
            return

        lines = content.split('\n')
        cut_count = min(self.copy_lines_spin.value(), len(lines))
        cut_text = '\n'.join(lines[:cut_count])
        remaining_text = '\n'.join(lines[cut_count:])

        clipboard = QApplication.clipboard()
        clipboard.setText(cut_text)
        self.result_text.setPlainText(remaining_text)
        QMessageBox.information(self, "成功", f"已剪切前 {cut_count} 行数据到剪贴板")

    def get_user_ids(self):
        """获取用户ID列表"""
        id_content = self.id_text.toPlainText().strip()
        if not id_content:
            return []
        return [line.strip() for line in id_content.split('\n') if line.strip()]

    def distribute_links(self, links, user_ids):
        """根据选择的逻辑分配链接"""
        if not user_ids:
            return links

        connector = self.connector_edit.text()

        if self.average_radio.isChecked():
            return self.average_distribution(links, user_ids, connector)
        elif self.custom_radio.isChecked():
            return self.custom_distribution(links, user_ids, connector)
        elif self.full_radio.isChecked():
            return self.full_distribution(links, user_ids, connector)

        return links

    def average_distribution(self, links, user_ids, connector):
        """平均分配 - 优先保证每个ID分配数量平均"""
        # 如果是乱序，先打乱链接
        if self.random_radio.isChecked():
            links = links.copy()
            random.shuffle(links)

        result = []
        user_count = len(user_ids)
        total_links = len(links)
        interval = self.interval_spin.value()

        # 计算平均分配：每个用户最多能分配多少个链接
        base_count = total_links // user_count  # 每个用户至少分配的数量
        remainder = total_links % user_count    # 剩余的链接数

        # 计算每个用户应该分配的链接数量
        user_quotas = []
        for i in range(user_count):
            if i < remainder:
                user_quotas.append(base_count + 1)  # 前remainder个用户多分配1个
            else:
                user_quotas.append(base_count)

        # 记录每个用户已分配的链接数
        user_assigned_counts = [0] * user_count
        current_user_index = 0
        links_assigned_to_current_user = 0
        link_index = 0

        while link_index < total_links:
            # 检查当前用户是否已达到配额
            if user_assigned_counts[current_user_index] >= user_quotas[current_user_index]:
                # 当前用户已达到配额，切换到下一个用户
                current_user_index = (current_user_index + 1) % user_count
                links_assigned_to_current_user = 0
                continue

            # 分配链接给当前用户
            user_id = user_ids[current_user_index]
            result.append(f"{links[link_index]}{connector}{user_id}")
            user_assigned_counts[current_user_index] += 1
            links_assigned_to_current_user += 1
            link_index += 1

            # 检查是否需要按间隔切换用户（但不能超过配额限制）
            if links_assigned_to_current_user >= interval:
                # 找到下一个还没达到配额的用户
                next_user_index = (current_user_index + 1) % user_count
                attempts = 0
                while (user_assigned_counts[next_user_index] >= user_quotas[next_user_index]
                       and attempts < user_count):
                    next_user_index = (next_user_index + 1) % user_count
                    attempts += 1

                if attempts < user_count:  # 找到了还没达到配额的用户
                    current_user_index = next_user_index
                    links_assigned_to_current_user = 0

        return result

    def custom_distribution(self, links, user_ids, connector):
        """自定义分配 - 先执行第一阶段，再执行第二阶段，支持乱序"""
        custom_count = self.custom_count_spin.value()
        custom_interval = self.custom_interval_spin.value()
        second_interval = self.second_interval_spin.value()

        # 如果是乱序，先打乱链接
        if self.random_radio.isChecked():
            links = links.copy()
            random.shuffle(links)

        result = []
        link_index = 0
        user_count = len(user_ids)

        # 第一阶段：每个ID分配指定数量的链接，按间隔切换ID
        user_link_counts = [0] * user_count  # 记录每个用户已分配的链接数
        current_user_index = 0
        links_assigned_to_current_user = 0

        # 第一阶段分配
        while link_index < len(links):
            # 检查是否所有用户都已分配够指定数量
            if all(count >= custom_count for count in user_link_counts):
                break

            # 如果当前用户已分配够数量，跳到下一个用户
            if user_link_counts[current_user_index] >= custom_count:
                current_user_index = (current_user_index + 1) % user_count
                links_assigned_to_current_user = 0
                continue

            user_id = user_ids[current_user_index]
            result.append(f"{links[link_index]}{connector}{user_id}")
            user_link_counts[current_user_index] += 1
            link_index += 1
            links_assigned_to_current_user += 1

            # 按间隔切换用户
            if links_assigned_to_current_user >= custom_interval:
                current_user_index = (current_user_index + 1) % user_count
                links_assigned_to_current_user = 0

        # 第二阶段：处理剩余链接
        remaining_links = links[link_index:]
        if remaining_links:
            if self.second_average_radio.isChecked():
                # 平均分配剩余链接，按间隔切换
                current_user_index = 0
                links_assigned_to_current_user = 0

                for link in remaining_links:
                    user_id = user_ids[current_user_index]
                    result.append(f"{link}{connector}{user_id}")
                    links_assigned_to_current_user += 1

                    # 按间隔切换用户
                    if links_assigned_to_current_user >= second_interval:
                        current_user_index = (current_user_index + 1) % user_count
                        links_assigned_to_current_user = 0
            else:  # cycle - 修复循环分配逻辑
                # 循环分配剩余链接 - 每个用户轮流获得链接
                for i, link in enumerate(remaining_links):
                    # 根据链接索引和间隔计算用户索引
                    user_index = (i // second_interval) % user_count
                    user_id = user_ids[user_index]
                    result.append(f"{link}{connector}{user_id}")

        return result

    def full_distribution(self, links, user_ids, connector):
        """拉满分配 - 每个用户ID都分配所有链接，支持间隔切换"""
        # 如果是乱序，先打乱链接
        if self.random_radio.isChecked():
            links = links.copy()
            random.shuffle(links)

        result = []
        interval = self.interval_spin.value()
        user_count = len(user_ids)

        # 创建每个用户的链接队列
        user_queues = {}
        for user_id in user_ids:
            user_queues[user_id] = []
            for link in links:
                user_queues[user_id].append(f"{link}{connector}{user_id}")

        # 按间隔从队列中取出链接
        current_user_index = 0

        while any(user_queues.values()):  # 还有用户有链接
            user_id = user_ids[current_user_index]

            if user_queues[user_id]:  # 当前用户还有链接
                # 按间隔数量取出链接
                for _ in range(min(interval, len(user_queues[user_id]))):
                    if user_queues[user_id]:
                        result.append(user_queues[user_id].pop(0))

            # 切换到下一个用户
            current_user_index = (current_user_index + 1) % user_count

        return result

    def extract_links(self):
        """提取视频链接"""
        text_content = self.input_text.toPlainText().strip()
        if not text_content:
            QMessageBox.warning(self, "警告", "请输入文本内容或加载文件")
            return

        # 提取链接的正则表达式
        url_pattern = r'https?://[^\s\u4e00-\u9fff]+'

        links = []
        lines = text_content.split('\n')

        for line in lines:
            # 查找链接
            url_matches = re.findall(url_pattern, line)
            for url in url_matches:
                # 清理链接末尾可能的标点符号（已修正正则表达式）
                url = re.sub(r'[，。！？；：""（）【】\s]+$', '', url)
                links.append(url)

        if not links:
            QMessageBox.warning(self, "警告", "未找到任何链接")
            return

        # 获取用户ID并分配链接
        user_ids = self.get_user_ids()
        if user_ids:
            distributed_links = self.distribute_links(links, user_ids)
        else:
            distributed_links = links

        # 显示结果
        result = '\n'.join(distributed_links)
        self.result_text.setPlainText(result)
        QMessageBox.information(self, "成功", f"成功提取 {len(distributed_links)} 个链接")

    def clear_input(self):
        """清空输入框"""
        self.input_text.clear()
        self.file_path_edit.clear()

    def save_results(self):
        """保存结果到文件"""
        result_content = self.result_text.toPlainText().strip()
        if not result_content:
            QMessageBox.warning(self, "警告", "没有结果可保存")
            return

        save_dir = self.save_dir_edit.text()
        if not os.path.exists(save_dir):
            QMessageBox.critical(self, "错误", "保存目录不存在")
            return

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"extracted_links_{timestamp}.txt"
        filepath = os.path.join(save_dir, filename)

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(result_content)
            QMessageBox.information(self, "成功", f"结果已保存到: {filepath}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        config = {
            "connector": self.connector_edit.text(),
            "save_dir": self.save_dir_edit.text(),
            "link_count": self.link_count_spin.value(),
            "id_count": self.id_count_spin.value(),
            "logic": ("average" if self.average_radio.isChecked() else
                     "custom" if self.custom_radio.isChecked() else "full"),
            "order": "sequential" if self.sequential_radio.isChecked() else "random",
            "interval": self.interval_spin.value(),
            "custom_count": self.custom_count_spin.value(),
            "custom_interval": self.custom_interval_spin.value(),
            "second_logic": "average" if self.second_average_radio.isChecked() else "cycle",
            "second_interval": self.second_interval_spin.value()
        }

        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            QMessageBox.information(self, "成功", "配置已保存")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存配置失败: {str(e)}")

    def load_config(self):
        """加载配置"""
        if not os.path.exists(self.config_file):
            return

        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            self.connector_edit.setText(config.get("connector", "----"))
            self.save_dir_edit.setText(config.get("save_dir", os.getcwd()))
            self.link_count_spin.setValue(config.get("link_count", 100))
            self.id_count_spin.setValue(config.get("id_count", 5))

            logic = config.get("logic", "average")
            if logic == "average":
                self.average_radio.setChecked(True)
            elif logic == "custom":
                self.custom_radio.setChecked(True)
            else:  # full
                self.full_radio.setChecked(True)

            if config.get("order", "sequential") == "sequential":
                self.sequential_radio.setChecked(True)
            else:
                self.random_radio.setChecked(True)

            self.interval_spin.setValue(config.get("interval", 1))
            self.custom_count_spin.setValue(config.get("custom_count", 10))
            self.custom_interval_spin.setValue(config.get("custom_interval", 1))

            if config.get("second_logic", "average") == "average":
                self.second_average_radio.setChecked(True)
            else:
                self.second_cycle_radio.setChecked(True)

            self.second_interval_spin.setValue(config.get("second_interval", 1))
        except Exception as e:
            print(f"加载配置失败: {str(e)}")

def main():
    app = QApplication(sys.argv)
    # 设置应用程序图标
    app.setWindowIcon(QIcon("黑盟链接提取.ico"))
    window = VideoLinkExtractor()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
