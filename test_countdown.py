#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试倒计时功能的脚本
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 本地网络验证17 import LicenseKeyGenerator, show_countdown_window

def test_countdown_window():
    """测试倒计时窗口"""
    print("测试倒计时窗口功能...")
    print("将显示10秒倒计时窗口")
    
    try:
        show_countdown_window(10, "测试许可证过期")
    except Exception as e:
        print(f"测试失败: {e}")

def generate_test_license():
    """生成测试卡密"""
    master_key = "YourSecretMasterKey1234567890"
    generator = LicenseKeyGenerator(master_key)
    
    # 生成15秒的测试卡密
    license_key = generator.generate_license_key(15, "seconds", "1", "TEST")
    print(f"生成的测试卡密（15秒有效期）: {license_key}")
    
    # 保存到文件
    with open(".license", "w") as f:
        f.write(license_key)
    print("卡密已保存到 .license 文件")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "window":
        test_countdown_window()
    elif len(sys.argv) > 1 and sys.argv[1] == "license":
        generate_test_license()
    else:
        print("用法:")
        print("  python test_countdown.py window   - 测试倒计时窗口")
        print("  python test_countdown.py license  - 生成测试卡密")
