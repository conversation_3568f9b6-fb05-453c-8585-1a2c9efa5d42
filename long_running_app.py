#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长时间运行的演示应用程序 - 用于测试卡密保护功能
"""

import time
import random

def main():
    """主程序"""
    print("🎉 欢迎使用长时间运行的演示应用程序！")
    print("这是一个受卡密保护的程序示例")
    print("程序将运行很长时间，用于测试许可证过期功能")
    
    # 模拟程序运行
    step = 1
    while True:
        print(f"程序运行中... 步骤 {step}")
        
        # 模拟一些工作
        result = sum(random.randint(1, 100) for _ in range(1000))
        print(f"计算结果: {result}")
        
        step += 1
        time.sleep(2)  # 等待2秒

if __name__ == "__main__":
    main()
