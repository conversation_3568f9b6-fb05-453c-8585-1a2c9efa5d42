#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 本地网络验证17 import LicenseKeyGenerator

def main():
    """生成测试卡密并保存"""
    master_key = "YourSecretMasterKey1234567890"
    generator = LicenseKeyGenerator(master_key)
    
    # 生成12秒的测试卡密
    license_key = generator.generate_license_key(12, "seconds", "1", "TEST")
    print(f"生成的测试卡密（12秒有效期）: {license_key}")
    
    # 保存到文件
    with open(".license", "w") as f:
        f.write(license_key)
    print("卡密已保存到 .license 文件")
    print("\n现在可以运行 demo_app_protected.py 来测试倒计时功能")

if __name__ == "__main__":
    main()
