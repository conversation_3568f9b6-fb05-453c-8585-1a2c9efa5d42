#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试卡密系统的功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 本地网络验证17 import LicenseKeyGenerator, LicenseValidator

def test_license_system():
    """测试卡密系统"""
    master_key = "YourSecretMasterKey1234567890"
    
    print("=== 卡密系统功能测试 ===\n")
    
    # 测试生成器
    generator = LicenseKeyGenerator(master_key)
    validator = LicenseValidator(master_key)
    
    # 测试不同时间单位的卡密生成
    test_cases = [
        (2, "hours", "小时"),
        (30, "minutes", "分钟"),
        (120, "seconds", "秒")
    ]
    
    for duration, unit, unit_name in test_cases:
        print(f"--- 测试 {duration}{unit_name} 有效期的卡密 ---")
        
        # 生成卡密
        license_key = generator.generate_license_key(duration, unit, "1", f"TEST-{unit}")
        print(f"生成的卡密: {license_key}")
        
        # 验证卡密
        result = validator.verify_license_key(license_key, "1")
        
        print(f"验证状态: {'有效' if result['valid'] else '无效'}")
        if result['valid']:
            print(f"剩余时间: {result['remaining_time_formatted']}")
            if 'duration' in result['data'] and 'unit' in result['data']:
                print(f"原始有效期: {result['data']['duration']}{unit_name}")
        print(f"消息: {result['message']}")
        print()

if __name__ == "__main__":
    test_license_system()
